<!-- (c) https://github.com/MontiCore/monticore -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- == PROJECT COORDINATES ============================================= -->

  <groupId>cd2gui</groupId>
  <artifactId>cd2guiCore</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>

  <!-- == PROJECT MODULES ================================================= -->

  <modules>
    <module>cd2guitagging</module>
    <module>generator</module>
    <module>input</module>
  </modules>

  <properties>
    <!-- .. Libraries ..................................................... -->
    <monticore.version>6.1.0</monticore.version>
    <javadsl.version>4.3.12</javadsl.version>
    <freemarker.version>2.3.28</freemarker.version>
    <guava.version>18.0</guava.version>
    <jsr305.version>3.0.0</jsr305.version>
    <junit.version>4.13</junit.version>
    <antlr.version>4.7.1</antlr.version>
    <logback.version>1.1.2</logback.version>
    <emf.common.version>2.5.0</emf.common.version>
    <emf.ecore.version>2.5.0</emf.ecore.version>
    <emf.ecore.xmi.version>2.5.0</emf.ecore.xmi.version>
    <se-commons.version>1.7.9</se-commons.version>

    <!-- .. Plugins ....................................................... -->
    <assembly.plugin>2.5.3</assembly.plugin>
    <compiler.plugin>3.2</compiler.plugin>
    <release.plugin>2.5.1</release.plugin>
    <source.plugin>2.4</source.plugin>
    <surefire.plugin>3.0.0-M3</surefire.plugin>
    <exec.maven.plugin>1.6.0</exec.maven.plugin>

    <!-- Classifiers -->
    <grammars.classifier>grammars</grammars.classifier>

    <!-- .. Misc .......................................................... -->
    <java.version>1.8</java.version>
    <wagon.provider.version>2.6</wagon.provider.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <project.test.resourceEncoding>UTF-8</project.test.resourceEncoding>
  </properties>

  <!-- == PROJECT METAINFORMATION ========================================= -->
  <name>CD2GUI</name>

  <organization>
    <name>SE RWTH Aachen</name>
    <url>http://www.se-rwth.de/</url>
  </organization>

  <mailingLists>
    <mailingList>
      <name>Developer</name>
      <post><EMAIL></post>
    </mailingList>
  </mailingLists>

  <!-- == DEFAULT BUILD SETTINGS =========================================== -->
  <build>
  <pluginManagement>
    <plugins>
      <!-- MontiCore Generation -->
      <plugin>
        <groupId>de.monticore.mojo</groupId>
        <artifactId>monticore-maven-plugin</artifactId>
        <version>${monticore.version}</version>
        <executions>
          <execution>
            <!--<configuration>
              <script>de/monticore/monticore_noemf.groovy</script>
              <templatePaths>src/main/java</templatePaths>
              <reportDirectory>target/reports</reportDirectory>
            </configuration>-->
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- Other Configuration -->
      <!--<plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${compiler.plugin}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>-->

      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${compiler.plugin}</version>
        <configuration>
          <useIncrementalCompilation>true</useIncrementalCompilation>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>

      <!-- Source Jar Configuration -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>${source.plugin}</version>
        <executions>
          <execution>
            <id>create source jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
            <configuration>
              <excludeResources>false</excludeResources>
              <includes>
                <include>**/*.java</include>
                <include>**/*.ftl</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Unit Test Executor -->
      <!--<plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${surefire.plugin}</version>
        <configuration>
          <argLine>-Dfile.encoding=UTF-8</argLine>
          <classpathDependencyExcludes>
            <classpathDependencyExclude>de.monticore:monticore-cli</classpathDependencyExclude>
          </classpathDependencyExcludes>
          <includes>
            <include>**/*TestSuite.java</include>
          </includes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>${release.plugin}</version>
        <configuration>
          <tagNameFormat>cd2gui-@{project.version}</tagNameFormat>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>${source.plugin}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>-->
    </plugins>
  </pluginManagement>
  </build>

  <dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.5</version>
    </dependency>
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>${freemarker.version}</version>
    </dependency>
    <dependency>
      <groupId>de.monticore</groupId>
      <artifactId>monticore-generator</artifactId>
      <version>${monticore.version}</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
   <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>${guava.version}</version>
    </dependency>

    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>${jsr305.version}</version>
    </dependency>

    <dependency>
      <groupId>org.antlr</groupId>
      <artifactId>antlr4-runtime</artifactId>
      <version>${antlr.version}</version>
    </dependency>

    <dependency>
      <groupId>de.se_rwth.commons</groupId>
      <artifactId>se-commons-groovy</artifactId>
      <version>${se-commons.version}</version>
    </dependency>

    <!-- MontiCore Dependencies -->
    <dependency>
      <groupId>de.monticore</groupId>
      <artifactId>monticore-runtime</artifactId>
      <version>${monticore.version}</version>
    </dependency>

    <dependency>
      <groupId>de.monticore</groupId>
      <artifactId>monticore-grammar</artifactId>
      <version>${monticore.version}</version>
    </dependency>

    <dependency>
      <groupId>de.monticore</groupId>
      <artifactId>monticore-grammar</artifactId>
      <version>${monticore.version}</version>
      <classifier>${grammars.classifier}</classifier>
      <scope>provided</scope>
    </dependency>

    <!-- .. Test Libraries ............................................... -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>de.monticore</groupId>
      <artifactId>monticore-runtime</artifactId>
      <version>${monticore.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>${logback.version}</version>
      <scope>test</scope>
    </dependency>
  
    <dependency>
      <groupId>de.monticore</groupId>
      <artifactId>javaDSL</artifactId>
      <version>${javadsl.version}</version>
    </dependency>
  </dependencies>
  </dependencyManagement>

  <!-- == DISTRIBUTION ==================================================== -->
  <distributionManagement>
    <repository>
      <id>se-nexus</id>
      <url>http://nexus.se.rwth-aachen.de/content/repositories/montigem-releases/</url>
    </repository>
    <snapshotRepository>
      <id>se-nexus</id>
      <url>http://nexus.se.rwth-aachen.de/content/repositories/montigem-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

  <repositories>
    <repository>
      <id>se-nexus</id>
      <url>https://nexus.se.rwth-aachen.de/content/groups/montigem/</url>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>se-nexus</id>
      <url>https://nexus.se.rwth-aachen.de/content/groups/montigem/</url>
    </pluginRepository>
  </pluginRepositories>
</project>
