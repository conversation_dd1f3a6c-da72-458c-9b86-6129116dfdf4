package carrental;

import mc.fenix.arrange.GemCard;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemGrid;
import mc.fenix.basic.GemText;
import mc.fenix.basic.GemButton;
import mc.fenix.navigation.GemNavItem;
import mc.fenix.table.GemTable;
import mc.fenix.table.GemTableHeader;
import mc.fenix.table.GemTableRow;
import mc.fenix.charts.GemBarChart;
import mc.fenix.charts.GemBarChartTypes.GemBarChartData;
import mc.fenix.charts.GemBulletChart;
import mc.fenix.charts.GemCandlestickChart;
import mc.fenix.charts.GemCandlestickChartTypes.GemData2CandlestickChart;
import mc.fenix.charts.GemGaugeChart;
import mc.fenix.charts.GemGaugeChartTypes.GemGaugeChartData;
import mc.fenix.charts.GemHeatmapChart;
import mc.fenix.charts.GemHeatmapChartTypes.GemHeatmapChartData;
import mc.fenix.charts.GemLineChart;
import mc.fenix.charts.LineChartTypes.GemLineChartData;
import mc.fenix.charts.LineChartTypes.GemLineChartEntry;
import mc.fenix.charts.GemPieChart;
import mc.fenix.charts.GemPieChartTypes.GemPieChartData;
import mc.fenix.charts.GemPieChartTypes.GemPieChartEntry;
import mc.fenix.charts.GemSunburstChart;
import mc.fenix.charts.GemSunburstChartTypes.GemSunburstData;
import mc.fenix.charts.GemSunburstChartTypes.GemSDNshort;
import mc.fenix.charts.GemScatterPlot;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotData;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotAxis;

page CarMetric() {

  carVisualizationPanel@GemCard(
    title = "Data Metrics Visualization",
    component = carVisualizationPanelColumn@GemColumn(
      rowGap = "15px",
      components = [
        
        carMetricsStatsCard@GemCard(
          title = "Statistics Summary",
          component = carStatsRow@GemRow(
            hAlign = "space-evenly",
            components = [
              carTotalAttrsText@GemText(value = "Total Attributes: 5")
            ]
          )
        ),
        
        carVisualizationGrid@GemRow(
          wrap = "wrap",
          hAlign = "space-evenly",
          colGap = "5px",
          rowGap = "5px",
          components = [
            
            car_manufacturerChartCard@GemCard(
              width = "auto",
              height = "auto",
              title = "manufacturer Analysis",
              component = @GemColumn(
                hAlign = "center",
                vAlign = "center",
                components = [
                  @GemPieChart(
                    data = getPieChartData4CarManufacturer(),
                    innerRadius = 50
                  )
                ]
              )
            ),
            
            car_statusChartCard@GemCard(
              width = "auto",
              height = "auto",
              title = "status Analysis",
              component = @GemColumn(
                hAlign = "center",
                vAlign = "center",
                components = [
                  @GemPieChart(
                    data = getPieChartData4CarStatus(),
                    innerRadius = 50
                  )
                ]
              )
            ),
            
            car_mileageChartCard@GemCard(
              width = "auto",
              height = "auto",
              title = "mileage Analysis",
              component = @GemColumn(
                hAlign = "center",
                vAlign = "center",
                components = [
                  @GemScatterPlot(
                    data = getScatterPlotData4CarMileage(),
                    xAxis = @GemScatterPlotAxis(label = "X Axis"),
                    yAxis = @GemScatterPlotAxis(label = "Y Axis")
                  )
                ]
              )
            )
            
          ]
        )
        
      ]
    )
  );

}
