package cd2gui.test.gui;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import cd2gui.data.ChartDetail;
import cd2gui.gui.GUIImplGenerator;
import de.monticore.cdbasis._ast.ASTCDClass;
import org.junit.Test;

import java.io.File;
import java.util.*;

import static org.junit.Assert.*;

/**
 * Test class for Java implementation file generation.
 * Tests the new functionality that generates Java files instead of FTL templates.
 */
public class JavaGenerationTest {

    @Test
    public void testJavaFileGeneration() {
        // Clean up any existing output directory first
        File outputDir = new File("build/cd2gui/guiImpl/gui");
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }

        // Create mock data
        Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = createMockMetricsMap();

        // Generate Java implementations
        GUIImplGenerator.generateGUIImplementations(metricsMap);

        // Verify output directory was created
        assertTrue("Output directory should be created", outputDir.exists());

        // Verify that Java files were generated
        File[] javaFiles = outputDir.listFiles((dir, name) -> name.endsWith(".java"));
        assertNotNull("Java files should be generated", javaFiles);
        assertTrue("At least one Java file should be generated", javaFiles.length > 0);

        // Verify specific file exists
        File carMetricFile = new File(outputDir, "CarMetric.java");
        assertTrue("CarMetric.java should be generated", carMetricFile.exists());

        // Verify file content contains expected method signatures
        assertTrue("File should contain chart data methods", carMetricFile.length() > 0);
    }

    @Test
    public void testEmptyMetricsHandling() {
        // Test with empty metrics map
        Map<ASTCDClass, List<AttributeMetric<?>>> emptyMap = new HashMap<>();

        // Should not throw exception
        GUIImplGenerator.generateGUIImplementations(emptyMap);
    }

    @Test
    public void testNullMetricsHandling() {
        // Test with null metrics map
        // Should not throw exception
        GUIImplGenerator.generateGUIImplementations(null);
    }

    /**
     * Creates mock metrics map for testing.
     *
     * @return Mock metrics map with sample data
     */
    private Map<ASTCDClass, List<AttributeMetric<?>>> createMockMetricsMap() {
        Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = new HashMap<>();

        // Create mock ASTCDClass
        ASTCDClass mockClass = new ASTCDClass() {
            @Override
            public String getName() {
                return "Car";
            }
        };

        // Create mock AttributeMetric list
        List<AttributeMetric<?>> metrics = new ArrayList<>();

        // Add pie chart metric
        ChartDetail<String> pieDetail = new ChartDetail<String>() {
            private String data = "mock pie data";

            @Override
            public void setData(String data) {
                this.data = data;
            }

            @Override
            public String getData() {
                return data;
            }
        };
        AttributeMetric<String> pieMetric = new AttributeMetric<>("manufacturer", ChartType.PIE_CHART, pieDetail);
        metrics.add(pieMetric);

        // Add bar chart metric
        ChartDetail<String> barDetail = new ChartDetail<String>() {
            private String data = "mock bar data";

            @Override
            public void setData(String data) {
                this.data = data;
            }

            @Override
            public String getData() {
                return data;
            }
        };
        AttributeMetric<String> barMetric = new AttributeMetric<>("status", ChartType.BAR_CHART, barDetail);
        metrics.add(barMetric);

        metricsMap.put(mockClass, metrics);
        return metricsMap;
    }

    /**
     * Recursively deletes a directory and all its contents.
     *
     * @param directory Directory to delete
     * @return true if deletion was successful
     */
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }
}
