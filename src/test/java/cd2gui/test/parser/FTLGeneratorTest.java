/* (c) https://github.com/MontiCore/monticore */
package cd2gui.test.parser;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import cd2gui.ftl.FTLGenerator;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.io.paths.MCPath;
import org.junit.Test;

import java.io.File;
import java.util.*;

import static org.junit.Assert.*;

/**
 * Test class for the refactored FTLGenerator.
 * Tests the integration with the existing CD2GUI template system.
 */
public class FTLGeneratorTest extends AbstractTest {

    @Test
    public void testFTLGeneratorStaticMethods() {
        // Test that FTLGenerator static methods work correctly
        // Since FTLGenerator now uses static methods, we don't need to create instances
        assertTrue("FTLGenerator class should be accessible", FTLGenerator.class != null);
    }

    @Test
    public void testEmptyMetricsMap() {
        // Test handling of empty metrics map
        Map<ASTCDClass, List<AttributeMetric<?>>> emptyMap = new HashMap<>();

        // Should not throw exception with empty map
        FTLGenerator.generateGUIImplementations(emptyMap);
    }

    @Test
    public void testNullMetricsMap() {
        // Test handling of null metrics map
        // Should not throw exception with null map
        FTLGenerator.generateGUIImplementations(null);
    }

    @Test
    public void testIntegrationWithExistingSystem() throws Exception {
        // Test integration with existing CD2GUI system
        String modelPath = "src/test/resources/Domain.cd";
        String targetPath = "build/test/ftl-integration";

        // Generate GUI using existing system
        generateGUI(modelPath, targetPath);

        // Verify that the system still works after our changes
        File targetDir = new File(targetPath);
        assertTrue("Target directory should exist", targetDir.exists());

        // Check that GUI files were generated (now we generate .java files instead of .gui files)
        File guiImplDir = new File(targetDir, "gui");
        if (guiImplDir.exists()) {
            File[] javaFiles = guiImplDir.listFiles((dir, name) -> name.endsWith(".java"));
            assertNotNull("Java implementation files should be generated", javaFiles);
            // Note: Java files are only generated if there are metrics, so we don't require them to exist
        }

        // The test passes if the system doesn't crash during generation
        assertTrue("System should complete without errors", true);
    }
}
