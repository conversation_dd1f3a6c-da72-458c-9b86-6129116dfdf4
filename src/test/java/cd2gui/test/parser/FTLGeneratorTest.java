/* (c) https://github.com/MontiCore/monticore */
package cd2gui.test.parser;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import cd2gui.ftl.FTLGenerator;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.io.paths.MCPath;
import org.junit.Test;

import java.io.File;
import java.util.*;

import static org.junit.Assert.*;

/**
 * Test class for the refactored FTLGenerator.
 * Tests the integration with the existing CD2GUI template system.
 */
public class FTLGeneratorTest extends AbstractTest {

    @Test
    public void testFTLGeneratorCreation() {
        // Test that FTLGenerator can be created successfully
        File targetDir = new File("build/test/ftl");
        MCPath hwcPath = new MCPath();
        String domainPackage = "test.domain";
        String domainName = "TestDomain";

        FTLGenerator generator = FTLGenerator.create(targetDir, hwcPath, domainPackage, domainName);
        assertNotNull("FTLGenerator should be created successfully", generator);
    }

    @Test
    public void testEmptyMetricsMap() {
        // Test handling of empty metrics map
        File targetDir = new File("build/test/ftl");
        FTLGenerator generator = FTLGenerator.create(targetDir, new MCPath(), "test.domain", "TestDomain");
        
        Map<ASTCDClass, List<AttributeMetric<?>>> emptyMap = new HashMap<>();
        
        // Should not throw exception with empty map
        generator.generateFTLTemplates(emptyMap);
    }

    @Test
    public void testNullMetricsMap() {
        // Test handling of null metrics map
        File targetDir = new File("build/test/ftl");
        FTLGenerator generator = FTLGenerator.create(targetDir, new MCPath(), "test.domain", "TestDomain");
        
        // Should not throw exception with null map
        generator.generateFTLTemplates(null);
    }

    @Test
    public void testIntegrationWithExistingSystem() throws Exception {
        // Test integration with existing CD2GUI system
        String modelPath = "src/test/resources/Domain.cd";
        String targetPath = "build/test/ftl-integration";
        
        // Generate GUI using existing system
        generateGUI(modelPath, targetPath);
        
        // Verify that the system still works after our changes
        File targetDir = new File(targetPath);
        assertTrue("Target directory should exist", targetDir.exists());
        
        // Check that GUI files were generated
        File[] guiFiles = targetDir.listFiles((dir, name) -> name.endsWith(".gui"));
        assertNotNull("GUI files should be generated", guiFiles);
        assertTrue("At least one GUI file should be generated", guiFiles.length > 0);
    }
}
