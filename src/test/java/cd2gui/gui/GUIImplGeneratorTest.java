package cd2gui.gui;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.File;
import java.util.*;

/**
 * Test class for GUIImplGenerator.
 * Tests GUI implementation file generation functionality.
 */
public class GUIImplGeneratorTest {

    private static final String OUTPUT_DIR = "build/cd2gui/guiImpl/gui/";

    @Test
    public void testGenerateGUIImplementationsWithNullInput() {
        // Clean up any existing output directory first
        File outputDir = new File(OUTPUT_DIR);
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }

        // Test that null input doesn't crash the system
        GUIImplGenerator.generateGUIImplementations(null);

        // Should not crash and should not create output directory for null input
        assertFalse("Output directory should not be created for null input", outputDir.exists());
    }

    @Test
    public void testGenerateGUIImplementationsWithEmptyInput() {
        // Clean up any existing output directory first
        File outputDir = new File(OUTPUT_DIR);
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }

        // Test that empty input doesn't crash the system
        Map<ASTCDClass, List<AttributeMetric<?>>> emptyMap = new HashMap<>();
        GUIImplGenerator.generateGUIImplementations(emptyMap);

        // Should not crash and should not create output directory for empty input
        assertFalse("Output directory should not be created for empty input", outputDir.exists());
    }

    @Test
    public void testGenerateGUIImplementationsWithValidInput() {
        // Clean up any existing output directory first
        File outputDir = new File(OUTPUT_DIR);
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }

        // Create mock data
        Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = createMockMetricsMap();

        // Generate GUI implementations
        GUIImplGenerator.generateGUIImplementations(metricsMap);

        // Verify output directory was created
        assertTrue("Output directory should be created for valid input", outputDir.exists());

        // Verify that Java files were generated
        File[] javaFiles = outputDir.listFiles((dir, name) -> name.endsWith(".java"));
        assertNotNull("Java files should be generated", javaFiles);
        assertTrue("At least one Java file should be generated", javaFiles.length > 0);

        // Verify specific file exists
        File carMetricFile = new File(outputDir, "CarMetric.java");
        assertTrue("CarMetric.java should be generated", carMetricFile.exists());
    }

    /**
     * Creates mock metrics map for testing.
     * @return Mock metrics map with sample data
     */
    private Map<ASTCDClass, List<AttributeMetric<?>>> createMockMetricsMap() {
        Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = new HashMap<>();
        
        // Create mock ASTCDClass
        ASTCDClass mockClass = new ASTCDClass() {
            @Override
            public String getName() {
                return "Car";
            }
        };

        // Create mock AttributeMetric list
        List<AttributeMetric<?>> metrics = new ArrayList<>();
        
        // Add pie chart metric
        AttributeMetric<String> pieMetric = new AttributeMetric<>("manufacturer", ChartType.PIE_CHART);
        metrics.add(pieMetric);
        
        // Add bar chart metric
        AttributeMetric<String> barMetric = new AttributeMetric<>("status", ChartType.BAR_CHART);
        metrics.add(barMetric);
        
        // Add line chart metric
        AttributeMetric<Double> lineMetric = new AttributeMetric<>("mileage", ChartType.LINE_CHART);
        metrics.add(lineMetric);
        
        // Add scatter plot metric
        AttributeMetric<Double> scatterMetric = new AttributeMetric<>("price", ChartType.SCATTER_PLOT);
        metrics.add(scatterMetric);

        metricsMap.put(mockClass, metrics);
        return metricsMap;
    }

    /**
     * Recursively deletes a directory and all its contents.
     * @param directory Directory to delete
     * @return true if deletion was successful
     */
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }
}
