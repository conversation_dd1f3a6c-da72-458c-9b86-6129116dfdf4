<<<<<<< HEAD
package cd2gui.data;

=======
package data;

import cd2gui.data.*;
>>>>>>> origin/sle25-final-implementation
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;

/**
 * Test class for AttributeMetric.
 *
 */
public class AttributeMetricTest {

    @Test
    public void testBarChartMetric() {
        BarChartDetail chartDetail = new BarChartDetail();
        chartDetail.addLabel("Label");
        chartDetail.addEntry("Entry", Arrays.asList(1, 2, 3));

        AttributeMetric<GemBarChartData> metric = new AttributeMetric<>(
<<<<<<< HEAD
            "attrTest",
            ChartType.BAR_CHART,
            chartDetail
=======
                "attrTest",
                ChartType.BAR_CHART,
                chartDetail
>>>>>>> origin/sle25-final-implementation
        );

        assertEquals(ChartType.BAR_CHART, metric.getType());
        assertTrue(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testLineChartMetric() {
        LineChartDetail chartDetail = new LineChartDetail();
        chartDetail.addLabel("Label");
        chartDetail.addEntry("Entry", Arrays.asList(1.0, 2.0, 3.0));

        AttributeMetric<GemLineChartData> metric = new AttributeMetric<>(
<<<<<<< HEAD
            "attrTest",
            ChartType.LINE_CHART,
            chartDetail
=======
                "attrTest",
                ChartType.LINE_CHART,
                chartDetail
>>>>>>> origin/sle25-final-implementation
        );

        assertEquals(ChartType.LINE_CHART, metric.getType());
        assertTrue(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testPieChartMetric() {
        PieChartDetail chartDetail = new PieChartDetail();
        chartDetail.addEntry("Entry", 100);

        AttributeMetric<GemPieChartData> metric = new AttributeMetric<>(
<<<<<<< HEAD
            "attrTest",
            ChartType.PIE_CHART,
            chartDetail
=======
                "attrTest",
                ChartType.PIE_CHART,
                chartDetail
>>>>>>> origin/sle25-final-implementation
        );

        assertEquals(ChartType.PIE_CHART, metric.getType());
        assertTrue(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testNonVisualizableMetric() {
        BarChartDetail chartDetail = new BarChartDetail();

        AttributeMetric<GemBarChartData> metric = new AttributeMetric<>(
<<<<<<< HEAD
            "attrTest",
            ChartType.NONE,
            chartDetail
=======
                "attrTest",
                ChartType.NONE,
                chartDetail
>>>>>>> origin/sle25-final-implementation
        );

        assertEquals(ChartType.NONE, metric.getType());
        assertFalse(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testToString() {
        BarChartDetail chartDetail = new BarChartDetail();
        AttributeMetric<GemBarChartData> metric = new AttributeMetric<>("attrTest", ChartType.BAR_CHART, chartDetail);

        String result = metric.toString();
        assertTrue(result.contains("AttributeMetric"));
        assertTrue(result.contains("BAR_CHART"));
        assertTrue(result.contains("isVisualizable=true"));
    }
}
