package cd2gui.ftl;

import cd2gui.gui.GUIImplGenerator;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.File;

/**
 * Test class for FTLGenerator.
 * Tests FTL template generation functionality.
 */
public class FTLGeneratorTest {

    private static final String OUTPUT_DIR = "build/generated/ftl/";

    @Test
    public void testGenerateFTLTemplatesWithNullInput() {
        // Clean up any existing output directory first
        File outputDir = new File(OUTPUT_DIR);
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }

        // Test that null input doesn't crash the system
        GUIImplGenerator.generateGUIImplementations(null);

        // Should not crash and should not create output directory for null input
        assertFalse("Output directory should not be created for null input", outputDir.exists());
    }

    @Test
    public void testGenerateFTLTemplatesWithEmptyMap() {
        // Clean up any existing output directory first
        File outputDir = new File(OUTPUT_DIR);
        if (outputDir.exists()) {
            deleteDirectory(outputDir);
        }

        // Test that empty map doesn't crash the system
        java.util.Map<de.monticore.cdbasis._ast.ASTCDClass, java.util.List<cd2gui.data.AttributeMetric<?>>> emptyMap =
            new java.util.HashMap<>();
        GUIImplGenerator.generateGUIImplementations(emptyMap);

        // Should not crash and should not create output directory for empty map
        assertFalse("Output directory should not be created for empty map", outputDir.exists());
    }

    @Test
    public void testFTLGeneratorClassExists() {
        // Basic test to ensure the FTLGenerator class can be instantiated
        assertNotNull("FTLGenerator class should exist", FTLGenerator.class);
    }

    /**
     * Recursively deletes a directory and its contents.
     *
     * @param directory Directory to delete
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
