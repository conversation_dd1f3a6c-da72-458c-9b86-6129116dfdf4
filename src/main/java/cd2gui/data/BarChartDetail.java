package cd2gui.data;

import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import mc.fenix.charts.gembarcharttypes.GemBarChartDataBuilder;
import mc.fenix.charts.gembarcharttypes.GemBarChartEntry;
import mc.fenix.charts.gembarcharttypes.GemBarChartEntryBuilder;

import java.util.List;
import java.util.Optional;

/**
 * BAR_CHART implementation. Created by UnifiedAttributeAnalyzer.createBarChart().
 *
 * <AUTHOR> Li
 */
public class BarChartDetail implements ChartDetail<GemBarChartData> {

    private GemBarChartData barChartData;
    private boolean stacked;
    private int maxValue;
    private int minValue;

    public BarChartDetail() {
        Optional<GemBarChartData> dataOpt = new GemBarChartDataBuilder().build();
        this.barChartData = dataOpt.orElse(null);
        this.stacked = false;
        this.maxValue = 100;
        this.minValue = 0;
    }

    public void addEntry(String label, List<Integer> data) {
        GemBarChartEntry entry = new GemBarChartEntryBuilder()
                .label(Optional.of(label))
                .data(data)
                .build().get();
        barChartData.addEntries(entry);
    }

    public void addLabel(String label) {
        barChartData.addLabels(label);
    }

    @Override
    public void setData(GemBarChartData data) {
        this.barChartData = data;
    }

    @Override
    public GemBarChartData getData() {
        return barChartData;
    }

    public void setStacked(boolean stacked) {
        this.stacked = stacked;
    }

    public boolean isStacked() {
        return stacked;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setMinValue(int minValue) {
        this.minValue = minValue;
    }

    public int getMinValue() {
        return minValue;
    }
}
