package cd2gui.data;

/**
 * Chart detail interface. Implemented by PieChartDetail, BarChartDetail, etc.
 *
 * @param <D> mc.fenix.charts data type
 * <AUTHOR> Li
 */
public interface ChartDetail<D> {

    /**
     * Sets mc.fenix.charts data.
     *
     * @param data chart data
     */
    void setData(D data);

    /**
     * Returns mc.fenix.charts data for Stage3.
     *
     * @return chart data
     */
    D getData();
}