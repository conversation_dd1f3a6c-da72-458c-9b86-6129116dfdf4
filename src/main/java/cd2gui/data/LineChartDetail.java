package cd2gui.data;

import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartDataBuilder;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntry;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntryBuilder;

import java.util.List;
import java.util.Optional;

/**
 * LINE_CHART implementation. Created by UnifiedAttributeAnalyzer.createLineChart().
 *
 * <AUTHOR> Li
 */
public class LineChartDetail implements ChartDetail<GemLineChartData> {

    private GemLineChartData lineChartData;
    private boolean enableBackgroundColor;
    private int maxValue;
    private int minValue;

    public LineChartDetail() {
        Optional<GemLineChartData> dataOpt = new GemLineChartDataBuilder().build();
        this.lineChartData = dataOpt.orElse(null);
        this.enableBackgroundColor = false;
        this.maxValue = 100;
        this.minValue = 0;
    }

    public void addEntry(String label, List<Double> data) {
        GemLineChartEntry entry = new GemLineChartEntryBuilder()
                .label(label)
                .data(data)
                .build().get();
        lineChartData.addEntries(entry);
    }

    public void addLabel(String label) {
        lineChartData.addLabels(label);
    }

    @Override
    public void setData(GemLineChartData data) {
        this.lineChartData = data;
    }

    @Override
    public GemLineChartData getData() {
        return lineChartData;
    }

    public void setEnableBackgroundColor(boolean enableBackgroundColor) {
        this.enableBackgroundColor = enableBackgroundColor;
    }

    public boolean isBackgroundColorEnabled() {
        return enableBackgroundColor;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setMinValue(int minValue) {
        this.minValue = minValue;
    }

    public int getMinValue() {
        return minValue;
    }
}
