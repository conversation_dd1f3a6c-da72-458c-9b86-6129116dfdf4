package cd2gui.data;

import mc.fenix.charts.gemscatterplottypes.GemScatterPlotData;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotDataBuilder;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotSet;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotSetBuilder;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotPoint;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotShape;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotAxis;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotAxisBuilder;

import java.util.List;
import java.util.Optional;

/**
 * SCATTER_PLOT implementation. Created by UnifiedAttributeAnalyzer.createScatterPlot().
 *
 * <AUTHOR> Li
 */
public class ScatterPlotDetail implements ChartDetail<GemScatterPlotData> {

    private GemScatterPlotData scatterPlotData;
    private GemScatterPlotAxis xAxis;
    private GemScatterPlotAxis yAxis;

    public ScatterPlotDetail() {
        Optional<GemScatterPlotData> dataOpt = new GemScatterPlotDataBuilder().build();
        this.scatterPlotData = dataOpt.orElse(null);
        this.xAxis = new GemScatterPlotAxisBuilder()
            .label("X Axis")
            .minValue(0.0)
            .maxValue(100.0)
            .stepWidth(10.0)
            .build();
        this.yAxis = new GemScatterPlotAxisBuilder()
            .label("Y Axis")
            .minValue(0.0)
            .maxValue(100.0)
            .stepWidth(10.0)
            .build();
    }

    public void addSet(String label, List<GemScatterPlotPoint> points,
                       Optional<GemScatterPlotShape> shape,
                       Optional<String> color,
                       Optional<String> hoverColor) {
        GemScatterPlotSet set = new GemScatterPlotSetBuilder()
                .label(label)
                .points(points)
                .shape(shape)
                .color(color)
                .hoverColor(hoverColor)
                .build().get();
        scatterPlotData.addSets(set);
    }

    public GemScatterPlotPoint createPoint(double x, double y, String label, Optional<Integer> pointRadius) {
        return new GemScatterPlotPoint(x, y, label, pointRadius);
    }

    @Override
    public void setData(GemScatterPlotData data) {
        this.scatterPlotData = data;
    }

    @Override
    public GemScatterPlotData getData() {
        return scatterPlotData;
    }

    public void setXAxis(GemScatterPlotAxis xAxis) {
        this.xAxis = xAxis;
    }

    public GemScatterPlotAxis getXAxis() {
        return xAxis;
    }

    public void setYAxis(GemScatterPlotAxis yAxis) {
        this.yAxis = yAxis;
    }

    public GemScatterPlotAxis getYAxis() {
        return yAxis;
    }
}
