package cd2gui.metric;

import cd2gui.data.*;
import cd2gui.util.Types;
import de.monticore.cdbasis._ast.ASTCDAttribute;
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import mc.fenix.charts.gembarcharttypes.GemBarChartDataBuilder;
import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import mc.fenix.charts.gempiecharttypes.GemPieChartDataBuilder;
import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartDataBuilder;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotData;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotDataBuilder;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotPoint;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotShape;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotAxis;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import de.monticore.cdbasis._ast.ASTCDClass;

/**
 * Core Stage2 analysis engine. Implements pattern recognition and scale-based chart generation.
 * Called by AttributeMetricIdentifier.processClassForVisualization().
 *
 * <AUTHOR> Yang
 */
public class UnifiedAttributeAnalyzer {
    /**
     * Main analysis method. Processes class structure and CD2GUIAttribute list from Stage1.
     * Generates empty chart specifications based on attribute types and measurement scales.
     *
     * @param clazz UML class AST structure from Stage1
     * @param attributes CD2GUIAttribute list from GuiModelFileCreator.attributeMap
     * @return list of AttributeMetric objects for Stage3
     * <AUTHOR> Yang
     */
    public List<AttributeMetric<?>> analyzeClassForVisualization(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        if (clazz == null || attributes == null) {
            Log.warn("Null parameters provided to analyzeClassForVisualization");
            return new ArrayList<>();
        }

        Log.info("Analyzing class " + clazz.getName() + " with " + attributes.size() + " attributes", "UnifiedAttributeAnalyzer");

        // Debug: Print all attributes
        for (CD2GUIAttribute attr : attributes) {
            Log.info("  Attribute: " + attr.getName() + ", Type: " + attr.getType() + ", IsAttribute: " + attr.isAttribute(), "UnifiedAttributeAnalyzer");
        }

        List<AttributeMetric<?>> charts = new ArrayList<>();

        // 1. Try to generate combination charts first
        List<AttributeMetric<?>> combinationCharts = generateCombinationCharts(clazz, attributes);
        Log.info("Generated " + combinationCharts.size() + " combination charts", "UnifiedAttributeAnalyzer");
        charts.addAll(combinationCharts);

        // 2. Generate single attribute charts for remaining suitable attributes
        List<AttributeMetric<?>> singleCharts = generateSingleAttributeCharts(clazz, attributes, new HashSet<>());
        Log.info("Generated " + singleCharts.size() + " single attribute charts", "UnifiedAttributeAnalyzer");
        charts.addAll(singleCharts);

        Log.info("Total charts generated for " + clazz.getName() + ": " + charts.size(), "UnifiedAttributeAnalyzer");

        // Debug: Print all generated charts
        for (AttributeMetric<?> chart : charts) {
            Log.info("  Chart: " + chart.getAttributeName() + ", Type: " + chart.getType() + ", Visualizable: " + chart.isVisualizableAttribute(), "UnifiedAttributeAnalyzer");
        }

        return charts;
    }

    /**
     * Pattern recognition phase. Analyzes attribute types to detect domain patterns.
     * Generates empty chart specifications based on measurement scales and business patterns.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list from attributeMap
     * @return list of pattern-based chart specifications
     * <AUTHOR> Yang
     */
    private List<AttributeMetric<?>> generateCombinationCharts(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        List<AttributeMetric<?>> charts = new ArrayList<>();

        // Geographic pattern: detect location-related attributes by type
        if (hasGeographicPattern(clazz, attributes)) {
            AttributeMetric<?> geoChart = createGeographicDistributionChart(clazz, attributes);
            if (geoChart != null) {
                charts.add(geoChart);
            }
        }

        // Categorical pattern: detect classification attributes
        if (hasCategoricalPattern(clazz, attributes)) {
            AttributeMetric<?> catChart = createCategoricalDistributionChart(clazz, attributes);
            if (catChart != null) {
                charts.add(catChart);
            }
        }

        // Performance pattern: capacity, performance metrics
        if (hasPerformancePattern(clazz, attributes)) {
            AttributeMetric<?> perfChart = createPerformanceChart(clazz, attributes);
            if (perfChart != null) charts.add(perfChart);
        }

        // Academic pattern: grades, scores, educational metrics
        if (hasAcademicPattern(clazz, attributes)) {
            AttributeMetric<?> academicChart = createAcademicChart(clazz, attributes);
            if (academicChart != null) charts.add(academicChart);
        }

        // User management pattern: roles, permissions, user attributes
        if (hasUserManagementPattern(clazz, attributes)) {
            AttributeMetric<?> userChart = createUserManagementChart(clazz, attributes);
            if (userChart != null) charts.add(userChart);
        }

        // Project management pattern: issues, tasks, project metrics
        if (hasProjectManagementPattern(clazz, attributes)) {
            AttributeMetric<?> projectChart = createProjectManagementChart(clazz, attributes);
            if (projectChart != null) charts.add(projectChart);
        }

        // Temporal pattern: date, time, timestamp attributes for trend analysis
        if (hasTemporalPattern(clazz, attributes)) {
            AttributeMetric<?> temporalChart = createTemporalChart(clazz, attributes);
            if (temporalChart != null) charts.add(temporalChart);
        }

        // Correlation pattern: two numeric attributes for scatter plot analysis
        if (hasCorrelationPattern(clazz, attributes)) {
            AttributeMetric<?> correlationChart = createCorrelationChart(clazz, attributes);
            if (correlationChart != null) charts.add(correlationChart);
        }

        return charts;
    }

    /**
     * Detects geographic pattern in class attributes using type information.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if geographic pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasGeographicPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> geoKeywords = Set.of("city", "country", "region", "plz", "zip", "postal", "address");

        return attributes.stream()
                .anyMatch(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean isGeoAttribute = geoKeywords.stream().anyMatch(keyword -> name.contains(keyword));
                    boolean isSuitableType = !attr.isNumeric(); // Geographic data is usually text/enum
                    return isGeoAttribute && isSuitableType;
                });
    }

    /**
     * Creates geographic distribution chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return geographic distribution pie chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createGeographicDistributionChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        // Find the best geographic attribute for distribution
        Optional<CD2GUIAttribute> geoAttr = attributes.stream()
                .filter(attr -> isGeographicAttribute(attr.getName()))
                .filter(attr -> !attr.isNumeric()) // Geographic data is usually text/enum
                .findFirst();

        if (geoAttr.isPresent()) {
            // Create pie chart specification for geographic distribution
            return createEmptyPieChart(geoAttr.get().getName());
        }

        return null;
    }

    /**
     * Checks if attribute name indicates geographic data.
     *
     * @param attributeName attribute name to check
     * @return true if geographic attribute
     * <AUTHOR> Yang
     */
    private boolean isGeographicAttribute(String attributeName) {
        String name = attributeName.toLowerCase();
        return name.contains("city") || name.contains("country") || name.contains("region") ||
                name.equals("plz") || name.contains("zip") || name.contains("postal") ||
                name.contains("address");
    }

    /**
     * Detects categorical pattern in class attributes.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if categorical pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasCategoricalPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        return attributes.stream()
                .anyMatch(attr -> {
                    // Traditional categorical data types
                    if (attr.isEnum() || attr.isBoolean()) {
                        return true;
                    }

                    // String attributes that represent categorical data
                    if (!attr.isNumeric() && !attr.isEnum() && !attr.isBoolean()) {
                        String name = attr.getName().toLowerCase();
                        return name.contains("name") || name.contains("title") || name.contains("type") ||
                                name.contains("category") || name.contains("status") || name.contains("level") ||
                                name.contains("role") || name.contains("department") || name.contains("location") ||
                                name.contains("manufacturer") || name.contains("brand") || name.contains("model");
                    }

                    return false;
                });
    }

    /**
     * Creates categorical distribution chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return categorical distribution pie chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createCategoricalDistributionChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        // Find the best categorical attribute - prioritize enum/boolean, then meaningful strings
        Optional<CD2GUIAttribute> catAttr = attributes.stream()
                .filter(attr -> {
                    if (attr.isEnum() || attr.isBoolean()) {
                        return true;
                    }
                    if (!attr.isNumeric() && !attr.isEnum() && !attr.isBoolean()) {
                        String name = attr.getName().toLowerCase();
                        return name.contains("name") || name.contains("title") || name.contains("type") ||
                                name.contains("category") || name.contains("status") || name.contains("level") ||
                                name.contains("role") || name.contains("department") || name.contains("location") ||
                                name.contains("manufacturer") || name.contains("brand") || name.contains("model");
                    }
                    return false;
                })
                .findFirst();

        if (catAttr.isPresent()) {
            return createEmptyPieChart(catAttr.get().getName());
        }

        return null;
    }

    /**
     * Detects performance pattern in class attributes.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if performance pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasPerformancePattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> performanceKeywords = Set.of("capacity", "speed", "throughput", "bandwidth",
                "performance", "efficiency", "load", "volume", "size");

        return attributes.stream()
                .anyMatch(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean isNumeric = attr.isNumeric();
                    boolean hasPerformanceKeyword = performanceKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    return isNumeric && hasPerformanceKeyword;
                });
    }

    /**
     * Creates performance metrics chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return performance bar chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createPerformanceChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> performanceKeywords = Set.of("capacity", "speed", "throughput", "bandwidth",
                "performance", "efficiency", "load", "volume", "size");

        Optional<CD2GUIAttribute> perfAttr = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean isNumeric = attr.isNumeric();
                    boolean hasPerformanceKeyword = performanceKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    return isNumeric && hasPerformanceKeyword;
                })
                .findFirst();

        if (perfAttr.isPresent()) {
            return createEmptyBarChart(perfAttr.get().getName());
        }

        return null;
    }

    /**
     * Detects academic pattern in class attributes.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if academic pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasAcademicPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> academicKeywords = Set.of("grade", "score", "rating", "mark", "points",
                "credits", "semester", "gpa", "exam", "test");

        return attributes.stream()
                .anyMatch(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean hasAcademicKeyword = academicKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    return hasAcademicKeyword;
                });
    }

    /**
     * Creates academic metrics chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return academic distribution chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createAcademicChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> academicKeywords = Set.of("grade", "score", "rating", "mark", "points",
                "credits", "semester", "gpa", "exam", "test");

        Optional<CD2GUIAttribute> academicAttr = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean hasAcademicKeyword = academicKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    return hasAcademicKeyword;
                })
                .findFirst();

        if (academicAttr.isPresent()) {
            // Academic data is often categorical (grades) or numeric (scores)
            if (academicAttr.get().isEnum() || academicAttr.get().isBoolean()) {
                return createEmptyPieChart(academicAttr.get().getName());
            } else {
                return createEmptyBarChart(academicAttr.get().getName());
            }
        }

        return null;
    }

    /**
     * Detects user management pattern in class attributes.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if user management pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasUserManagementPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> userKeywords = Set.of("role", "permission", "access", "privilege", "status",
                "level", "rank", "authority", "rights");

        return attributes.stream()
                .anyMatch(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean hasUserKeyword = userKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    boolean isEnumOrString = attr.isEnum() || !attr.isNumeric();
                    return hasUserKeyword && isEnumOrString;
                });
    }

    /**
     * Creates user management chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return user management pie chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createUserManagementChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> userKeywords = Set.of("role", "permission", "access", "privilege", "status",
                "level", "rank", "authority", "rights");

        Optional<CD2GUIAttribute> userAttr = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean hasUserKeyword = userKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    boolean isEnumOrString = attr.isEnum() || !attr.isNumeric();
                    return hasUserKeyword && isEnumOrString;
                })
                .findFirst();

        if (userAttr.isPresent()) {
            return createEmptyPieChart(userAttr.get().getName());
        }

        return null;
    }

    /**
     * Detects project management pattern in class attributes.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if project management pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasProjectManagementPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> projectKeywords = Set.of("state", "status", "priority", "progress", "phase",
                "milestone", "task", "issue", "ticket", "workflow");

        return attributes.stream()
                .anyMatch(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean hasProjectKeyword = projectKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    return hasProjectKeyword;
                });
    }

    /**
     * Creates project management chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return project management pie chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createProjectManagementChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> projectKeywords = Set.of("state", "status", "priority", "progress", "phase",
                "milestone", "task", "issue", "ticket", "workflow");

        Optional<CD2GUIAttribute> projectAttr = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean hasProjectKeyword = projectKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    return hasProjectKeyword;
                })
                .findFirst();

        if (projectAttr.isPresent()) {
            return createEmptyPieChart(projectAttr.get().getName());
        }

        return null;
    }

    /**
     * Detects temporal pattern for time-based trend analysis.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if temporal pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasTemporalPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> temporalKeywords = Set.of("date", "time", "created", "updated", "modified",
                "timestamp", "when", "start", "end", "deadline");

        return attributes.stream()
                .anyMatch(attr -> {
                    String name = attr.getName().toLowerCase();
                    String typeName = attr.getType().getTypeInfo().getName();
                    boolean hasTemporalKeyword = temporalKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    boolean isTemporalType = typeName.contains("Date") || typeName.contains("Time") ||
                            typeName.contains("Timestamp") || typeName.contains("LocalDate");
                    return hasTemporalKeyword || isTemporalType;
                });
    }

    /**
     * Creates temporal trend chart specification.
     * Generates LINE_CHART specification to show trends over time.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return temporal trend line chart specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createTemporalChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        Set<String> temporalKeywords = Set.of("date", "time", "created", "updated", "modified",
                "timestamp", "when", "start", "end", "deadline");

        Optional<CD2GUIAttribute> temporalAttr = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    String typeName = attr.getType().getTypeInfo().getName();
                    boolean hasTemporalKeyword = temporalKeywords.stream()
                            .anyMatch(keyword -> name.contains(keyword));
                    boolean isTemporalType = typeName.contains("Date") || typeName.contains("Time") ||
                            typeName.contains("Timestamp") || typeName.contains("LocalDate");
                    return hasTemporalKeyword || isTemporalType;
                })
                .findFirst();

        if (temporalAttr.isPresent()) {
            return createEmptyLineChart(temporalAttr.get().getName());
        }

        return null;
    }

    /**
     * Detects correlation pattern for scatter plot analysis.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return true if correlation pattern detected
     * <AUTHOR> Yang
     */
    private boolean hasCorrelationPattern(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        // Find numeric attributes suitable for correlation analysis
        List<CD2GUIAttribute> numericAttrs = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean isNumeric = attr.isNumeric();
                    boolean notIdentifier = !name.contains("id") && !name.contains("uuid");
                    return isNumeric && notIdentifier;
                })
                .collect(Collectors.toList());

        // Need at least 2 numeric attributes for correlation
        return numericAttrs.size() >= 2;
    }

    /**
     * Creates correlation scatter plot chart specification.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list
     * @return correlation scatter plot specification
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createCorrelationChart(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        // Find two best numeric attributes for correlation
        List<CD2GUIAttribute> numericAttrs = attributes.stream()
                .filter(attr -> {
                    String name = attr.getName().toLowerCase();
                    boolean isNumeric = attr.isNumeric();
                    boolean notIdentifier = !name.contains("id") && !name.contains("uuid");
                    return isNumeric && notIdentifier;
                })
                .limit(2)
                .collect(Collectors.toList());

        if (numericAttrs.size() >= 2) {
            // Use first numeric attribute as primary for scatter plot
            CD2GUIAttribute primaryAttr = numericAttrs.get(0);
            return createEmptyScatterPlot(primaryAttr.getName());
        }

        return null;
    }

    /**
     * Single attribute analysis. Uses CD2GUIAttribute list directly.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list from attributeMap
     * @param usedAttributes attributes already used in combination charts
     * @return list of single attribute charts
     * <AUTHOR> Yang
     */
    private List<AttributeMetric<?>> generateSingleAttributeCharts(ASTCDClass clazz,
                                                                   List<CD2GUIAttribute> attributes,
                                                                   Set<String> usedAttributes) {
        List<AttributeMetric<?>> charts = new ArrayList<>();

        for (CD2GUIAttribute attribute : attributes) {
            String attrName = attribute.getName();

            // Skip if already used in combination charts
            if (usedAttributes.contains(attrName)) continue;

            // Skip if not suitable for single visualization using CD2GUIAttribute methods
            if (!isSuitableForSingleVisualization(attribute)) continue;

            // Create chart specification based on attribute type (no real data needed)
            if (attribute.isAttribute()) {
                AttributeMetric<?> chart = createAttributeMetricFromCD2GUI(attribute);
                if (chart != null) {
                    charts.add(chart);
                }
            }
        }

        return charts;
    }

    /**
     * Checks if CD2GUIAttribute is suitable for single visualization.
     *
     * @param attribute CD2GUIAttribute to check
     * @return true if suitable for single visualization
     * <AUTHOR> Yang
     */
    private boolean isSuitableForSingleVisualization(CD2GUIAttribute attribute) {
        // Use CD2GUIAttribute's type checking methods
        if (!attribute.isNumeric() && !attribute.isEnum() && !attribute.isBoolean()) {
            return false;
        }

        String name = attribute.getName().toLowerCase();

        // Exclude identifier attributes
        if (name.contains("id") || name.contains("uuid") || name.contains("key")) {
            return false;
        }

        // Include meaningful single attributes
        return name.contains("age") || name.contains("count") || name.contains("amount") ||
                name.contains("price") || name.contains("score") || name.contains("rating") ||
                attribute.isEnum() || attribute.isBoolean();
    }

    /**
     * Creates AttributeMetric from CD2GUIAttribute type analysis.
     *
     * @param attribute CD2GUIAttribute to analyze
     * @return AttributeMetric or null if not suitable
     * <AUTHOR> Yang
     */
    private AttributeMetric<?> createAttributeMetricFromCD2GUI(CD2GUIAttribute attribute) {
        if (attribute == null) {
            return null;
        }

        // Generate chart specification based on attribute type
        try {
            if (attribute.isEnum() || attribute.isBoolean()) {
                return createEmptyPieChart(attribute.getName());
            } else if (attribute.isNumeric()) {
                return createEmptyScatterPlot(attribute.getName());
            } else {
                return createEmptyBarChart(attribute.getName());
            }
        } catch (Exception e) {
            // If chart creation fails, return null
            return null;
        }
    }

    /**
     * Creates empty pie chart metric with proper null checking.
     * Called by analyzeClassForVisualization() for PIE_CHART type attributes.
     * Fixed: Ensures non-null return to prevent FTL template errors.
     *
     * @param attributeName Name of the attribute for chart generation
     * @return Non-null AttributeMetric with initialized PieChartDetail
     */
    private AttributeMetric<?> createEmptyPieChart(String attributeName) {
        PieChartDetail pieChartDetail = new PieChartDetail();

        // Add a default entry to prevent empty chart issues
        pieChartDetail.addEntry("No Data", 1);

        pieChartDetail.setInnerRadius(50);
        return new AttributeMetric<>(attributeName, ChartType.PIE_CHART, pieChartDetail);
    }

    /**
     * Creates empty bar chart metric with proper initialization.
     * Called by analyzeClassForVisualization() for BAR_CHART type attributes.
     * Fixed: Ensures non-null return to prevent FTL template errors.
     *
     * @param attributeName Name of the attribute for chart generation
     * @return Non-null AttributeMetric with initialized BarChartDetail
     */
    private AttributeMetric<?> createEmptyBarChart(String attributeName) {
        BarChartDetail barChartDetail = new BarChartDetail();

        // Add default data to prevent empty chart issues
        barChartDetail.addLabel("No Data");
        barChartDetail.addEntry("No Data", Arrays.asList(1));

        barChartDetail.setStacked(false);
        barChartDetail.setMinValue(0);
        barChartDetail.setMaxValue(100);

        return new AttributeMetric<>(attributeName, ChartType.BAR_CHART, barChartDetail);
    }

    /**
     * Creates empty line chart metric with proper initialization.
     * Called by analyzeClassForVisualization() for LINE_CHART type attributes.
     * Fixed: Ensures non-null return to prevent FTL template errors.
     *
     * @param attributeName Name of the attribute for chart generation
     * @return Non-null AttributeMetric with initialized LineChartDetail
     */
    private AttributeMetric<?> createEmptyLineChart(String attributeName) {
        LineChartDetail lineChartDetail = new LineChartDetail();

        Optional<GemLineChartData> lineDataOpt = new GemLineChartDataBuilder().build();
        if (lineDataOpt.isPresent()) {
            lineChartDetail.setData(lineDataOpt.get());
        }

        lineChartDetail.setEnableBackgroundColor(true);
        lineChartDetail.setMinValue(0);
        lineChartDetail.setMaxValue(100);

        return new AttributeMetric<>(attributeName, ChartType.LINE_CHART, lineChartDetail);
    }

    /**
     * Creates empty scatter plot metric with proper initialization.
     * Called by analyzeClassForVisualization() for SCATTER_PLOT type attributes.
     * Fixed: Ensures non-null return to prevent FTL template errors.
     *
     * @param attributeName Name of the attribute for chart generation
     * @return Non-null AttributeMetric with initialized ScatterPlotDetail
     */

    private AttributeMetric<?> createEmptyScatterPlot(String attributeName) {
        ScatterPlotDetail scatterPlotDetail = new ScatterPlotDetail();

        Optional<GemScatterPlotData> scatterDataOpt = new GemScatterPlotDataBuilder().build();
        if (scatterDataOpt.isPresent()) {
            scatterPlotDetail.setData(scatterDataOpt.get());
        }

        // Configure default axes
        GemScatterPlotAxis xAxis = new GemScatterPlotAxis(
                Optional.of("Index"),
                Optional.of(0.0),
                Optional.of(100.0),
                Optional.of(10.0)
        );

        GemScatterPlotAxis yAxis = new GemScatterPlotAxis(
                Optional.of(attributeName),
                Optional.of(0.0),
                Optional.of(100.0),
                Optional.of(10.0)
        );

        scatterPlotDetail.setXAxis(xAxis);
        scatterPlotDetail.setYAxis(yAxis);

        return new AttributeMetric<>(attributeName, ChartType.SCATTER_PLOT, scatterPlotDetail);
    }
}
