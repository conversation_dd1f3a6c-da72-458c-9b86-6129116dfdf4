package cd2gui.gui;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartDetail;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;

import java.util.*;

/**
 * Test class for GUIImplGenerator with FreeMarker templates.
 */
public class TestGUIImplGenerator {

    public static void main(String[] args) {
        System.out.println("Testing GUIImplGenerator with FreeMarker templates...");
        
        try {
            // Create mock data
            Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = createMockMetricsMap();
            
            // Generate GUI implementations
            GUIImplGenerator.generateGUIImplementations(metricsMap);
            
            System.out.println("GUI implementation generation completed successfully!");
            
        } catch (Exception e) {
            System.err.println("Error during GUI implementation generation: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Creates mock metrics map for testing.
     * @return Mock metrics map with sample data
     */
    private static Map<ASTCDClass, List<AttributeMetric<?>>> createMockMetricsMap() {
        Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = new HashMap<>();
        
        // Create mock ASTCDClass
        ASTCDClass mockClass = new ASTCDClass() {
            @Override
            public String getName() {
                return "Car";
            }
        };

        // Create mock AttributeMetric list
        List<AttributeMetric<?>> metrics = new ArrayList<>();
        
        // Add pie chart metric
        ChartDetail<String> pieDetail = new ChartDetail<String>() {
            private String data = "mock pie data";
            
            @Override
            public void setData(String data) { this.data = data; }
            
            @Override
            public String getData() { return data; }
        };
        AttributeMetric<String> pieMetric = new AttributeMetric<>("manufacturer", ChartType.PIE_CHART, pieDetail);
        metrics.add(pieMetric);
        
        // Add bar chart metric
        ChartDetail<String> barDetail = new ChartDetail<String>() {
            private String data = "mock bar data";
            
            @Override
            public void setData(String data) { this.data = data; }
            
            @Override
            public String getData() { return data; }
        };
        AttributeMetric<String> barMetric = new AttributeMetric<>("status", ChartType.BAR_CHART, barDetail);
        metrics.add(barMetric);

        metricsMap.put(mockClass, metrics);
        return metricsMap;
    }
}
