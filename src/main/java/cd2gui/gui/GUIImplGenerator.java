package cd2gui.gui;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Generates Java implementation files for UMLP-Tool based on existing AttributeMetric objects.
 * Called by CD2GUITool.main() after GUI generation.
 * Generates files directly to build/cd2gui/guiImpl path as configured in car-rental frontend build.gradle.
 */
public class GUIImplGenerator {

    private static final String OUTPUT_DIR = getOutputDirectory();
    private static final Set<String> generatedTemplates = new HashSet<>();
    private static String currentOutputPath;

    /**
     * Gets the output directory for GUI implementation files.
     * Outputs to build/cd2gui/guiImpl directory to match car-rental frontend build.gradle configuration.
     *
     * @return Output directory path with trailing slash
     */
    private static String getOutputDirectory() {
        try {
            // Use current working directory + build/cd2gui/guiImpl
            String currentDir = System.getProperty("user.dir");
            String outputPath = currentDir + File.separator + "build" + File.separator + "cd2gui" + File.separator + "guiImpl" + File.separator;
            return outputPath;
        } catch (Exception e) {
            // Fallback to system temp directory
            String tempDir = System.getProperty("java.io.tmpdir");
            String outputPath = tempDir + File.separator + "cd2gui-guiImpl" + File.separator;
            Log.warn("Using fallback output directory: " + outputPath);
            return outputPath;
        }
    }

    /**
     * Generates Java implementation files for all metrics in the provided map.
     * Creates Java classes with chart data methods for GUI pages.
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     * @param targetDir  Target directory for output (e.g., "frontend/build")
     */
    public static void generateGUIImplementations(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap, String targetDir) {
        if (metricsMap == null || metricsMap.isEmpty()) {
            Log.warn("No metrics map provided for GUI implementation generation");
            return;
        }

        // Clear previous generation tracking
        generatedTemplates.clear();

        // Create output directory using the provided target directory
        // The targetDir already contains the path to build/cd2gui, so we just need to add guiImpl
        String outputPath = targetDir + File.separator + "guiImpl" + File.separator;
        currentOutputPath = outputPath;
        File outputDir = new File(outputPath);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        Log.info("Starting GUI implementation generation for " + metricsMap.size() + " classes", "GUIImplGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            String className = entry.getKey().getName();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (metrics != null && !metrics.isEmpty()) {
                generateGUIClassImplementation(className, metrics);
                Log.info("Generated GUI implementation for class " + className + " with " + metrics.size() + " chart methods", "GUIImplGenerator");
            }
        }

        Log.info("GUI implementation generation completed", "GUIImplGenerator");
    }

    /**
     * Generates Java implementation files for all metrics in the provided map.
     * Uses default output directory for backward compatibility.
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     */
    public static void generateGUIImplementations(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        generateGUIImplementations(metricsMap, getOutputDirectory());
    }

    /**
     * Generates a chart data method for a specific metric.
     * Called by generateGUIClassImplementation() for each AttributeMetric.
     *
     * @param javaContent StringBuilder to append the method code
     * @param className   Name of the domain class
     * @param metric      AttributeMetric object containing chart information
     */
    private static void generateChartDataMethod(StringBuilder javaContent, String className, AttributeMetric<?> metric) {
        if (metric == null || metric.getAttributeName() == null) {
            Log.warn("Invalid metric provided for chart data method generation");
            return;
        }

        String attributeName = metric.getAttributeName();
        ChartType chartType = metric.getType();

        if (chartType == null || chartType == ChartType.NONE) {
            Log.warn("Skipping chart data method generation for attribute " + attributeName + " - no suitable chart type");
            return;
        }

        switch (chartType) {
            case PIE_CHART:
                generatePieChartDataMethod(javaContent, className, attributeName);
                break;
            case BAR_CHART:
                generateBarChartDataMethod(javaContent, className, attributeName);
                break;
            case LINE_CHART:
                generateLineChartDataMethod(javaContent, className, attributeName);
                break;
            case SCATTER_PLOT:
                generateScatterPlotDataMethod(javaContent, className, attributeName);
                break;
            default:
                Log.warn("Unsupported chart type for method generation: " + chartType);
                break;
        }
    }

    /**
     * Writes Java implementation file content to file.
     * Called by generateGUIClassImplementation().
     * Writes to build/cd2gui/guiImpl directory.
     *
     * @param fileName Name of the output file
     * @param content  Java implementation content
     */
    private static void writeJavaFile(String fileName, String content) {
        // Check for duplicates
        if (generatedTemplates.contains(fileName)) {
            Log.warn("Skipping duplicate Java implementation file: " + fileName);
            return;
        }

        try {
            // Write to the configured output directory
            String outputDir = currentOutputPath != null ? currentOutputPath : OUTPUT_DIR;
            File file = new File(outputDir + "gui" + File.separator + fileName);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(content);
            }

            // Mark as generated
            generatedTemplates.add(fileName);
            Log.info("Generated Java implementation file: " + fileName, "GUIImplGenerator");
        } catch (IOException e) {
            Log.error("Failed to write Java implementation file: " + fileName + " - " + e.getMessage());
        }
    }

    /**
     * Generates Java implementation file for a specific class with chart data methods.
     * Creates a Java class in gui package with methods for each chart metric.
     *
     * @param className Name of the domain class
     * @param metrics   List of AttributeMetric objects for this class
     */
    private static void generateGUIClassImplementation(String className, List<AttributeMetric<?>> metrics) {
        if (className == null || metrics == null || metrics.isEmpty()) {
            Log.warn("Invalid parameters for GUI class implementation generation");
            return;
        }

        String guiClassName = className + "Metric";
        String fileName = guiClassName + ".java";

        StringBuilder javaContent = new StringBuilder();

        // Package declaration - use gui package to match generated GUI files
        javaContent.append("package gui;\n\n");

        // Imports
        javaContent.append("import ").append(className.toLowerCase()).append(".").append(className).append(";\n");
        javaContent.append("import umlp.jsweet.extension.annotation.Component;\n");
        javaContent.append("import java.util.*;\n");
        javaContent.append("import java.util.stream.Collectors;\n");
        javaContent.append("import java.util.Arrays;\n\n");

        // Add chart-specific imports based on metrics
        Set<String> chartImports = new HashSet<>();
        for (AttributeMetric<?> metric : metrics) {
            ChartType chartType = metric.getType();
            if (chartType != null && chartType != ChartType.NONE) {
                switch (chartType) {
                    case PIE_CHART:
                        chartImports.add("import mc.fenix.charts.gempiecharttypes.GemPieChartData;\n");
                        chartImports.add("import mc.fenix.charts.gempiecharttypes.GemPieChartDataBuilder;\n");
                        chartImports.add("import mc.fenix.charts.gempiecharttypes.GemPieChartEntry;\n");
                        chartImports.add("import mc.fenix.charts.gempiecharttypes.GemPieChartEntryBuilder;\n");
                        break;
                    case BAR_CHART:
                        chartImports.add("import mc.fenix.charts.GemBarChartData;\n");
                        chartImports.add("import mc.fenix.charts.GemBarChartDataBuilder;\n");
                        break;
                    case LINE_CHART:
                        chartImports.add("import mc.fenix.charts.GemLineChartData;\n");
                        chartImports.add("import mc.fenix.charts.GemLineChartDataBuilder;\n");
                        chartImports.add("import mc.fenix.charts.GemLineChartEntry;\n");
                        chartImports.add("import mc.fenix.charts.GemLineChartEntryBuilder;\n");
                        break;
                    case SCATTER_PLOT:
                        chartImports.add("import mc.fenix.charts.gemscatterplottypes.GemScatterPlotData;\n");
                        chartImports.add("import mc.fenix.charts.gemscatterplottypes.GemScatterPlotDataBuilder;\n");
                        chartImports.add("import mc.fenix.charts.gemscatterplottypes.GemScatterPlotEntry;\n");
                        chartImports.add("import mc.fenix.charts.gemscatterplottypes.GemScatterPlotEntryBuilder;\n");
                        chartImports.add("import mc.fenix.charts.GemScatterPlotAxis;\n");
                        chartImports.add("import mc.fenix.charts.GemScatterPlotAxisBuilder;\n");
                        break;
                }
            }
        }

        // Add chart imports
        for (String chartImport : chartImports) {
            javaContent.append(chartImport);
        }
        javaContent.append("\n");

        // Class declaration - extends the generated TOP class
        javaContent.append("@Component\n");
        javaContent.append("public class ").append(guiClassName).append(" extends ").append(guiClassName).append("TOP {\n\n");

        // Generate chart data methods for each metric
        for (AttributeMetric<?> metric : metrics) {
            generateChartDataMethod(javaContent, className, metric);
        }

        // Close class
        javaContent.append("}\n");

        writeJavaFile(fileName, javaContent.toString());
    }

    /**
     * Generates pie chart data method for Java implementation.
     * Creates method for UMLP-Tool to generate getPieChartData4{ClassName}{AttributeName}() method.
     *
     * @param javaContent   StringBuilder to append the method code
     * @param className     Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generatePieChartDataMethod(StringBuilder javaContent, String className, String attributeName) {
        String methodName = "getPieChartData4" + className + capitalize(attributeName);

        javaContent.append("    /**\n");
        javaContent.append("     * Generates pie chart data for ").append(attributeName).append(" distribution.\n");
        javaContent.append("     * @return GemPieChartData for ").append(attributeName).append(" attribute\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemPieChartData ").append(methodName).append("() {\n");

        // Generate data retrieval logic - use page data (following processDataForCarStatus.ftl pattern)
        javaContent.append("        // Get data from the current page context\n");
        javaContent.append("        var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        javaContent.append("        Map<String, Integer> distribution = new HashMap<>();\n\n");

        javaContent.append("        // Collect unique values for labels\n");
        javaContent.append("        List<String> labels = new ArrayList<>();\n");
        javaContent.append("        for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        javaContent.append("            String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("().toString();\n");
        javaContent.append("            if (").append(attributeName).append(" != null && !labels.contains(").append(attributeName).append(")) {\n");
        javaContent.append("                labels.add(").append(attributeName).append(");\n");
        javaContent.append("            }\n");
        javaContent.append("        }\n\n");
        javaContent.append("        // Create entries for each unique value\n");
        javaContent.append("        List<GemPieChartEntry> entries = new ArrayList<>();\n");
        javaContent.append("        for (String label : labels) {\n");
        javaContent.append("            entries.add(new GemPieChartEntryBuilder()\n");
        javaContent.append("                .value(0)\n");
        javaContent.append("                .label(label)\n");
        javaContent.append("                .build().get());\n");
        javaContent.append("        }\n\n");
        javaContent.append("        // Count occurrences for each value\n");
        javaContent.append("        for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        javaContent.append("            String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("().toString();\n");
        javaContent.append("            if (").append(attributeName).append(" != null) {\n");
        javaContent.append("                int index = labels.indexOf(").append(attributeName).append(");\n");
        javaContent.append("                if (index >= 0) {\n");
        javaContent.append("                    entries.get(index).setValue(entries.get(index).getValue() + 1);\n");
        javaContent.append("                }\n");
        javaContent.append("            }\n");
        javaContent.append("        }\n\n");

        // Build pie chart data using entries (following Dashboard.java pattern)\n");
        javaContent.append("        GemPieChartData data = new GemPieChartDataBuilder().entries(entries).build().get();\n");
        javaContent.append("        return data;\n");
        javaContent.append("    }\n\n");
    }

    /**
     * Generates bar chart data method for Java implementation.
     * Creates method for UMLP-Tool to generate getBarChartData4{ClassName}{AttributeName}() method.
     *
     * @param javaContent   StringBuilder to append the method code
     * @param className     Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateBarChartDataMethod(StringBuilder javaContent, String className, String attributeName) {
        String methodName = "getBarChartData4" + className + capitalize(attributeName);

        javaContent.append("    /**\n");
        javaContent.append("     * Generates bar chart data for ").append(attributeName).append(" distribution.\n");
        javaContent.append("     * @return GemBarChartData for ").append(attributeName).append(" attribute\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemBarChartData ").append(methodName).append("() {\n");

        // Generate data retrieval logic - use page data instead of Manager
        javaContent.append("        var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        javaContent.append("        Map<String, Integer> distribution = new HashMap<>();\n\n");

        javaContent.append("        for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        javaContent.append("            String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        javaContent.append("            if (").append(attributeName).append(" != null) {\n");
        javaContent.append("                distribution.put(").append(attributeName).append(", distribution.getOrDefault(").append(attributeName).append(", 0) + 1);\n");
        javaContent.append("            }\n");
        javaContent.append("        }\n\n");

        // Generate chart data building
        javaContent.append("        GemBarChartDataBuilder builder = new GemBarChartDataBuilder();\n");
        javaContent.append("        for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n");
        javaContent.append("            builder.addLabel(entry.getKey());\n");
        javaContent.append("            builder.addEntry(entry.getKey(), Arrays.asList(entry.getValue()));\n");
        javaContent.append("        }\n\n");
        javaContent.append("        return builder.build().get();\n");
        javaContent.append("    }\n\n");
    }

    /**
     * Generates line chart data method for Java implementation.
     * Creates method for UMLP-Tool to generate getLineChartData4{ClassName}{AttributeName}() method.
     * Used for temporal data visualization (dates, timestamps, time series).
     *
     * @param javaContent   StringBuilder to append the method code
     * @param className     Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateLineChartDataMethod(StringBuilder javaContent, String className, String attributeName) {
        String methodName = "getLineChartData4" + className + capitalize(attributeName);

        javaContent.append("    /**\n");
        javaContent.append("     * Generates line chart data for ").append(attributeName).append(" temporal analysis.\n");
        javaContent.append("     * @return GemLineChartData for ").append(attributeName).append(" attribute\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemLineChartData ").append(methodName).append("() {\n");

        // Generate basic line chart implementation with sample data
        javaContent.append("        // Line chart implementation for ").append(attributeName).append("\n");
        javaContent.append("        List<GemLineChartEntry> entries = new ArrayList<>();\n");
        javaContent.append("        List<Double> sampleData = Arrays.asList(1.0, 2.0, 3.0);\n");
        javaContent.append("        GemLineChartEntry entry = new GemLineChartEntryBuilder()\n");
        javaContent.append("            .label(\"Sample Data\")\n");
        javaContent.append("            .data(sampleData)\n");
        javaContent.append("            .build().get();\n");
        javaContent.append("        entries.add(entry);\n\n");
        javaContent.append("        GemLineChartDataBuilder builder = new GemLineChartDataBuilder();\n");
        javaContent.append("        builder.entries(entries);\n");
        javaContent.append("        return builder.build().get();\n");
        javaContent.append("    }\n\n");
    }

    /**
     * Generates scatter plot data method for Java implementation.
     * Creates method for UMLP-Tool to generate getScatterPlotData4{ClassName}{AttributeName}() method.
     * Used for correlation analysis between numeric attributes.
     *
     * @param javaContent   StringBuilder to append the method code
     * @param className     Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateScatterPlotDataMethod(StringBuilder javaContent, String className, String attributeName) {
        String methodName = "getScatterPlotData4" + className + capitalize(attributeName);
        String xAxisMethodName = "getScatterPlotXAxis4" + className + capitalize(attributeName);
        String yAxisMethodName = "getScatterPlotYAxis4" + className + capitalize(attributeName);

        // Generate scatter plot data method
        javaContent.append("    /**\n");
        javaContent.append("     * Generates scatter plot data for ").append(attributeName).append(" correlation analysis.\n");
        javaContent.append("     * @return GemScatterPlotData for ").append(attributeName).append(" attribute\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemScatterPlotData ").append(methodName).append("() {\n");

        // Generate scatter plot implementation with real data (following Dashboard.java pattern)
        javaContent.append("        List<").append(className).append("> ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        javaContent.append("        if (").append(className.toLowerCase()).append("s == null) {\n");
        javaContent.append("            return null;\n");
        javaContent.append("        }\n\n");
        javaContent.append("        // Create labels (x axis points) - use indices\n");
        javaContent.append("        List<String> labels = new ArrayList<>();\n");
        javaContent.append("        List<Double> dataPoints = new ArrayList<>();\n");
        javaContent.append("        \n");
        javaContent.append("        // Process each instance to build scatter plot data\n");
        javaContent.append("        for (int i = 0; i < ").append(className.toLowerCase()).append("s.size(); i++) {\n");
        javaContent.append("            ").append(className).append(" ").append(className.toLowerCase()).append(" = ").append(className.toLowerCase()).append("s.get(i);\n");
        javaContent.append("            Number ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        javaContent.append("            if (").append(attributeName).append(" != null) {\n");
        javaContent.append("                labels.add(\"").append(className).append(" \" + (i + 1));\n");
        javaContent.append("                dataPoints.add(").append(attributeName).append(".doubleValue());\n");
        javaContent.append("            }\n");
        javaContent.append("        }\n\n");
        javaContent.append("        // Create scatter plot entry (following Dashboard.java pattern)\n");
        javaContent.append("        GemScatterPlotEntry entry = new GemScatterPlotEntryBuilder()\n");
        javaContent.append("            .label(\"").append(capitalize(attributeName)).append(" Data\")\n");
        javaContent.append("            .data(dataPoints)\n");
        javaContent.append("            .build().get();\n\n");
        javaContent.append("        List<GemScatterPlotEntry> entries = Arrays.asList(entry);\n");
        javaContent.append("        \n");
        javaContent.append("        GemScatterPlotData data = new GemScatterPlotDataBuilder()\n");
        javaContent.append("            .entries(entries)\n");
        javaContent.append("            .labels(labels)\n");
        javaContent.append("            .build().get();\n\n");
        javaContent.append("        return data;\n");
        javaContent.append("    }\n\n");

        // Generate X axis method
        javaContent.append("    /**\n");
        javaContent.append("     * Generates X axis configuration for ").append(attributeName).append(" scatter plot.\n");
        javaContent.append("     * @return GemScatterPlotAxis for X axis\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemScatterPlotAxis ").append(xAxisMethodName).append("() {\n");
        javaContent.append("        return new GemScatterPlotAxisBuilder()\n");
        javaContent.append("            .label(\"Index\")\n");
        javaContent.append("            .minValue(0.0)\n");
        javaContent.append("            .maxValue(20.0)\n");
        javaContent.append("            .stepWidth(1.0)\n");
        javaContent.append("            .build().get();\n");
        javaContent.append("    }\n\n");

        // Generate Y axis method
        javaContent.append("    /**\n");
        javaContent.append("     * Generates Y axis configuration for ").append(attributeName).append(" scatter plot.\n");
        javaContent.append("     * @return GemScatterPlotAxis for Y axis\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemScatterPlotAxis ").append(yAxisMethodName).append("() {\n");
        javaContent.append("        return new GemScatterPlotAxisBuilder()\n");
        javaContent.append("            .label(\"").append(capitalize(attributeName)).append("\")\n");
        javaContent.append("            .minValue(0.0)\n");
        javaContent.append("            .maxValue(200000.0)\n");
        javaContent.append("            .stepWidth(20000.0)\n");
        javaContent.append("            .build().get();\n");
        javaContent.append("    }\n\n");
    }

    /**
     * Capitalizes the first letter of a string.
     * Used for generating method names and variable names.
     *
     * @param str Input string
     * @return String with first letter capitalized
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
