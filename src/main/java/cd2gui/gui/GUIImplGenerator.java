package cd2gui.gui;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Generates Java implementation files for UMLP-Tool based on existing AttributeMetric objects.
 * Called by CD2GUITool.main() after GUI generation.
 * Generates files directly to build/cd2gui/guiImpl path as configured in car-rental frontend build.gradle.
 */
public class GUIImplGenerator {

    private static final String OUTPUT_DIR = getOutputDirectory();
    private static final Set<String> generatedTemplates = new HashSet<>();
    private static Configuration freemarkerConfig;

    /**
     * Gets the output directory for GUI implementation files.
     * Outputs to build/cd2gui/guiImpl directory to match car-rental frontend build.gradle configuration.
     *
     * @return Output directory path with trailing slash
     */
    private static String getOutputDirectory() {
        try {
            // Use current working directory + build/cd2gui/guiImpl
            String currentDir = System.getProperty("user.dir");
            String outputPath = currentDir + File.separator + "build" + File.separator + "cd2gui" + File.separator + "guiImpl" + File.separator;
            return outputPath;
        } catch (Exception e) {
            // Fallback to system temp directory
            String tempDir = System.getProperty("java.io.tmpdir");
            String outputPath = tempDir + File.separator + "cd2gui-guiImpl" + File.separator;
            Log.warn("Using fallback output directory: " + outputPath);
            return outputPath;
        }
    }

    /**
     * Initializes FreeMarker configuration for template processing.
     * Sets up template directory and encoding.
     */
    private static void initializeFreeMarkerConfig() {
        if (freemarkerConfig == null) {
            freemarkerConfig = new Configuration(Configuration.VERSION_2_3_31);
            try {
                // Set template directory to resources/tpl/ftl
                freemarkerConfig.setClassForTemplateLoading(GUIImplGenerator.class, "/tpl/ftl");
                freemarkerConfig.setDefaultEncoding("UTF-8");
                Log.info("FreeMarker configuration initialized successfully", "GUIImplGenerator");
            } catch (Exception e) {
                Log.error("Failed to initialize FreeMarker configuration: " + e.getMessage());
                throw new RuntimeException("FreeMarker initialization failed", e);
            }
        }
    }

    /**
     * Generates Java implementation files for all metrics in the provided map.
     * Creates Java classes with chart data methods for GUI pages.
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     */
    public static void generateGUIImplementations(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        if (metricsMap == null || metricsMap.isEmpty()) {
            Log.warn("No metrics map provided for GUI implementation generation");
            return;
        }

        // Initialize FreeMarker configuration
        initializeFreeMarkerConfig();

        // Clear previous generation tracking
        generatedTemplates.clear();

        // Create output directory
        File outputDir = new File(OUTPUT_DIR);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        Log.info("Starting GUI implementation generation for " + metricsMap.size() + " classes", "GUIImplGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            String className = entry.getKey().getName();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (metrics != null && !metrics.isEmpty()) {
                generateGUIClassImplementation(className, metrics);
                Log.info("Generated GUI implementation for class " + className + " with " + metrics.size() + " chart methods", "GUIImplGenerator");
            }
        }

        Log.info("GUI implementation generation completed", "GUIImplGenerator");
    }



    /**
     * Writes Java implementation file content to file.
     * Called by generateGUIClassImplementation().
     * Writes to build/cd2gui/guiImpl directory.
     *
     * @param fileName Name of the output file
     * @param content Java implementation content
     */
    private static void writeJavaFile(String fileName, String content) {
        // Check for duplicates
        if (generatedTemplates.contains(fileName)) {
            Log.warn("Skipping duplicate Java implementation file: " + fileName);
            return;
        }

        try {
            // Write to build/cd2gui/guiImpl directory
            File file = new File(OUTPUT_DIR + "gui" + File.separator + fileName);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(content);
            }

            // Mark as generated
            generatedTemplates.add(fileName);
            Log.info("Generated Java implementation file: " + fileName, "GUIImplGenerator");
        } catch (IOException e) {
            Log.error("Failed to write Java implementation file: " + fileName + " - " + e.getMessage());
        }
    }

    /**
     * Generates Java implementation file for a specific class with chart data methods using FreeMarker templates.
     * Creates a Java class in gui package with methods for each chart metric.
     *
     * @param className Name of the domain class
     * @param metrics List of AttributeMetric objects for this class
     */
    private static void generateGUIClassImplementation(String className, List<AttributeMetric<?>>> metrics) {
        if (className == null || metrics == null || metrics.isEmpty()) {
            Log.warn("Invalid parameters for GUI class implementation generation");
            return;
        }

        String guiClassName = className + "Metric";
        String fileName = guiClassName + ".java";

        try {
            // Get the main Java class template
            Template template = freemarkerConfig.getTemplate("java-class-template.ftl");

            // Prepare template data model
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("className", className);
            dataModel.put("domainPackage", className.toLowerCase()); // Assume package name is lowercase class name
            dataModel.put("metrics", createTemplateMetrics(metrics));
            dataModel.put("chartTypes", getUniqueChartTypes(metrics));

            // Process template
            StringWriter stringWriter = new StringWriter();
            template.process(dataModel, stringWriter);
            String javaContent = stringWriter.toString();

            // Write generated Java file
            writeJavaFile(fileName, javaContent);

        } catch (IOException | TemplateException e) {
            Log.error("Failed to generate Java implementation for class " + className + ": " + e.getMessage());
        }
    }

    /**
     * Creates template-friendly metric objects for FreeMarker processing.
     * Converts AttributeMetric objects to simple maps for template use.
     *
     * @param metrics List of AttributeMetric objects
     * @return List of template-friendly metric maps
     */
    private static List<Map<String, Object>> createTemplateMetrics(List<AttributeMetric<?>> metrics) {
        List<Map<String, Object>> templateMetrics = new ArrayList<>();

        for (AttributeMetric<?> metric : metrics) {
            Map<String, Object> templateMetric = new HashMap<>();
            templateMetric.put("attributeName", metric.getAttributeName());
            templateMetric.put("type", metric.getType().toString());
            templateMetric.put("isNumeric", isNumericType(metric));
            templateMetric.put("isEnum", isEnumType(metric));
            templateMetrics.add(templateMetric);
        }

        return templateMetrics;
    }

    /**
     * Gets unique chart types used in the metrics for import generation.
     *
     * @param metrics List of AttributeMetric objects
     * @return Set of unique chart type strings
     */
    private static Set<String> getUniqueChartTypes(List<AttributeMetric<?>> metrics) {
        return metrics.stream()
                .map(metric -> metric.getType().toString())
                .filter(type -> !type.equals("NONE"))
                .collect(Collectors.toSet());
    }

    /**
     * Determines if an attribute metric represents a numeric type.
     *
     * @param metric AttributeMetric to check
     * @return true if numeric, false otherwise
     */
    private static boolean isNumericType(AttributeMetric<?> metric) {
        // This is a simplified check - in a real implementation, you'd check the actual type
        // For now, assume attributes with certain names are numeric
        String attrName = metric.getAttributeName().toLowerCase();
        return attrName.contains("price") || attrName.contains("cost") || attrName.contains("amount")
               || attrName.contains("count") || attrName.contains("number") || attrName.contains("id");
    }

    /**
     * Determines if an attribute metric represents an enum type.
     *
     * @param metric AttributeMetric to check
     * @return true if enum, false otherwise
     */
    private static boolean isEnumType(AttributeMetric<?> metric) {
        // This is a simplified check - in a real implementation, you'd check the actual type
        // For now, assume attributes with certain names are enums
        String attrName = metric.getAttributeName().toLowerCase();
        return attrName.contains("status") || attrName.contains("type") || attrName.contains("category");
    }









    /**
     * Capitalizes the first letter of a string.
     * Used for generating method names and variable names.
     *
     * @param str Input string
     * @return String with first letter capitalized
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
