package cd2gui.gui;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Generates Java implementation files for UMLP-Tool based on existing AttributeMetric objects.
 * Called by CD2GUITool.main() after GUI generation.
 * Generates files directly to build/cd2gui/guiImpl path as configured in car-rental frontend build.gradle.
 */
public class GUIImplGenerator {

    private static final String OUTPUT_DIR = getOutputDirectory();
    private static final Set<String> generatedTemplates = new HashSet<>();

    /**
     * Gets the output directory for FTL templates.
     * Outputs to both build/generated/ftl and backend/src/main/resources/ftl directories.
     *
     * @return Output directory path with trailing slash
     */
    private static String getOutputDirectory() {
        try {
            // Always use cd2gui project directory, not the calling project directory
            String cd2guiDir = getCd2guiProjectDirectory();
            return cd2guiDir + File.separator + "build" + File.separator + "generated" + File.separator + "ftl" + File.separator;
        } catch (Exception e) {

            // Fallback to system temp directory
            String tempDir = System.getProperty("java.io.tmpdir");
            String outputPath = tempDir + File.separator + "cd2gui-ftl" + File.separator;
            Log.warn("Using fallback output directory: " + outputPath);
            return outputPath;
        }
    }

    /**
     * Gets the cd2gui project directory regardless of where this code is called from.
     * Uses the classpath to locate the cd2gui jar/classes and derive the project path.
     */
    private static String getCd2guiProjectDirectory() {
        try {
            // Use the location of this class to find cd2gui directory
            String classPath = GUIImplGenerator.class.getProtectionDomain().getCodeSource().getLocation().getPath();
            // Decode URL-encoded path (fixes %20 space issue)
            classPath = URLDecoder.decode(classPath, StandardCharsets.UTF_8);

            // If running from a jar file
            if (classPath.endsWith(".jar")) {
                File jarFile = new File(classPath);
                File cd2guiDir = jarFile.getParentFile().getParentFile().getParentFile();
                if (cd2guiDir.getName().equals("cd2gui") || new File(cd2guiDir, "src\\main\\java\\cd2gui").exists()) {
                    return cd2guiDir.getAbsolutePath();
                }
            }

            String currentDir = System.getProperty("user.dir");

            // If already in cd2gui directory, continue with the current directory
            if (currentDir.endsWith("cd2gui") || new File(currentDir, "src\\main\\java\\cd2gui").exists()) {
                return currentDir;
            }

            // If all else fails, use the current directory
            Log.warn("Could not locate cd2gui project directory, using current directory: " + currentDir);
            return currentDir;

        } catch (Exception e) {
            Log.warn("Error locating cd2gui directory: " + e.getMessage());
            return System.getProperty("user.dir");
        }
    }

    /**
     * Generates FTL templates for all metrics in the provided map.
     * Calls generateTemplateForMetric() for each AttributeMetric.
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     */
    public static void generateFTLTemplates(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        if (metricsMap == null || metricsMap.isEmpty()) {
            Log.warn("No metrics map provided for FTL generation");
            return;
        }

        // Clear previous generation tracking
        generatedTemplates.clear();

        // Create an output directory
        File outputDir = new File(OUTPUT_DIR);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        Log.info("Starting FTL template generation for " + metricsMap.size() + " classes", "FTLGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            String className = entry.getKey().getName();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (metrics != null && !metrics.isEmpty()) {
                for (AttributeMetric<?> metric : metrics) {
                    generateTemplateForMetric(className, metric);
                }
                Log.info("Generated " + metrics.size() + " FTL templates for class " + className, "FTLGenerator");
            }
        }

        Log.info("FTL template generation completed", "FTLGenerator");
    }

    /**
     * Generates FTL template for a specific metric.
     * Called by generateFTLTemplates() for each AttributeMetric.
     *
     * @param className Name of the class containing the attribute
     * @param metric AttributeMetric object containing chart information
     */
    private static void generateTemplateForMetric(String className, AttributeMetric<?> metric) {
        if (metric == null || metric.getAttributeName() == null) {
            Log.warn("Invalid metric provided for FTL generation");
            return;
        }

        String attributeName = metric.getAttributeName();
        ChartType chartType = metric.getType();

        if (chartType == null || chartType == ChartType.NONE) {
            Log.warn("Skipping FTL generation for attribute " + attributeName + " - no suitable chart type");
            return;
        }

        // Generate method name and file name based on class and attribute
        String methodName = "processDataFor" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        // Check for duplicates
        if (generatedTemplates.contains(fileName)) {
            Log.warn("Skipping duplicate FTL generation for " + fileName);
            return;
        }

        // Generate template content
        try {
            generateFromTemplate(className, attributeName, methodName, fileName, chartType);
            generatedTemplates.add(fileName);
            Log.info("Generated template-based FTL: " + fileName, "SimpleFTLGenerator");
        } catch (Exception e) {
            Log.error("Failed to generate template for " + fileName + ": " + e.getMessage());
        }
    }

    /**
     * Generates FTL content from template file using simple string replacement.
     */
    private static void generateFromTemplate(String className, String attributeName,
                                             String methodName, String fileName, ChartType chartType) throws IOException {

        // Determine template file based on chart type
        String templateFileName = getTemplateFileName(chartType);

        // Read template content with the template file name
        String templateContent = readTemplateFile(templateFileName);

        // Determine attribute characteristics for conditional logic
        boolean isNumeric = isNumericAttribute(attributeName);
        boolean isEnum = isEnumAttribute(attributeName);
        boolean isDate = isDateAttribute(attributeName);

        // Process FTL conditionals manually
        String processedContent = processFTLConditionals(templateContent, isNumeric, isEnum, isDate);

        // Replace placeholders
        String content = processedContent
                .replace("${className}", className)
                .replace("${className?lower_case}", className.toLowerCase())
                .replace("${attributeName}", attributeName)
                .replace("${attributeName?cap_first}", capitalize(attributeName));

        // Create final FTL content with proper headers
        String ftlContent = "${tc.signature(\"ast\", \"domainClass\", \"" + attributeName + "\")}\n" +
                "${cd4c.method(\"public " + getReturnType(chartType) + " " + methodName + "()\")}\n\n" +
                content;

        // Write to both locations (dual output)
        writeTemplateFile(fileName, ftlContent);
    }

    /**
     * Process FTL conditionals manually since we can't use FTL engine.
     */
    private static String processFTLConditionals(String template, boolean isNumeric, boolean isEnum, boolean isDate) {
        StringBuilder result = new StringBuilder();
        String[] lines = template.split("\n");

        boolean skipBlock = false;
        boolean inConditional = false;

        for (String line : lines) {
            if (line.trim().startsWith("<#if isNumeric>")) {
                inConditional = true;
                skipBlock = !isNumeric;
                continue;
            } else if (line.trim().startsWith("<#elseif isEnum>")) {
                if (inConditional) {
                    skipBlock = !isEnum;
                    continue;
                }
            } else if (line.trim().startsWith("<#elseif isDate>")) {
                if (inConditional) {
                    skipBlock = !isDate;
                    continue;
                }
            } else if (line.trim().startsWith("<#else>")) {
                if (inConditional) {
                    skipBlock = isNumeric || isEnum || isDate; // Skip else if any condition was true
                    continue;
                }
            } else if (line.trim().startsWith("</#if>")) {
                inConditional = false;
                skipBlock = false;
                continue;
            }

            if (!skipBlock && !line.trim().startsWith("<#--")) {
                result.append(line).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * Get template file name based on chart type.
     */
    private static String getTemplateFileName(ChartType chartType) {
        switch (chartType) {
            case PIE_CHART:
                return "pie-chart-template.ftl";
            case BAR_CHART:
                return "bar-chart-template.ftl";
            case LINE_CHART:
                return "line-chart-template.ftl";
            case SCATTER_PLOT:
                return "scatter-plot-template.ftl";
            default:
                return "pie-chart-template.ftl";
        }
    }

    /**
     * Get return type based on chart type.
     */
    private static String getReturnType(ChartType chartType) {
        switch (chartType) {
            case PIE_CHART:
                return "GemPieChartData";
            case BAR_CHART:
                return "GemBarChartData";
            case LINE_CHART:
                return "GemLineChartData";
            case SCATTER_PLOT:
                return "GemScatterPlotData";
            default:
                return "GemPieChartData";
        }
    }

    /**
     * Simple heuristic to determine if attribute is numeric.
     */
    private static boolean isNumericAttribute(String attributeName) {
        String lower = attributeName.toLowerCase();
        return lower.contains("mileage") || lower.contains("fee") ||
                lower.contains("discount") || lower.contains("price") ||
                lower.contains("amount") || lower.contains("count");
    }

    /**
     * Simple heuristic to determine if attribute is enum.
     */
    private static boolean isEnumAttribute(String attributeName) {
        String lower = attributeName.toLowerCase();
        return lower.contains("status") || lower.contains("type") ||
                lower.contains("category") || lower.contains("state") ||
                lower.contains("name") || lower.contains("manufacturer");
    }

    /**
     * Simple heuristic to determine if attribute is date.
     */
    private static boolean isDateAttribute(String attributeName) {
        String lower = attributeName.toLowerCase();
        return lower.contains("date") || lower.contains("time") ||
                lower.contains("created") || lower.contains("updated");
    }

    /**
     * Writes FTL template content to file.
     * Called by generateXxxChartTemplate() methods.
     * Writes to both build/generated/ftl and backend/src/main/resources/ftl directories.
     *
     * @param fileName Name of the output file
     * @param content FTL template content
     */
    private static void writeTemplateFile(String fileName, String content) {
        // Check for duplicates
        if (generatedTemplates.contains(fileName)) {
            Log.warn("Skipping duplicate FTL template: " + fileName);
            return;
        }

        try {
            // Write to build/generated/ftl directory
            File file1 = new File(OUTPUT_DIR + fileName);
            File parentDir1 = file1.getParentFile();
            if (parentDir1 != null && !parentDir1.exists()) {
                parentDir1.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file1)) {
                writer.write(content);
            }

            // Also write to backend/src/main/resources/ftl directory for UMLP-Tool
            String currentDir = System.getProperty("user.dir");
            String backendFtlPath = currentDir + File.separator + "backend" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "ftl" + File.separator;
            File file2 = new File(backendFtlPath + fileName);
            File parentDir2 = file2.getParentFile();
            if (parentDir2 != null && !parentDir2.exists()) {
                parentDir2.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file2)) {
                writer.write(content);
            }

            // Mark as generated
            generatedTemplates.add(fileName);
            Log.info("Generated FTL template: " + fileName, "FTLGenerator");
        } catch (IOException e) {
            Log.error("Failed to write FTL template file: " + fileName + " - " + e.getMessage());
        }
    }

    /**
     * Read template file from cd2gui project directory using direct path reading.
     *
     * @param templateFileName Name of the template file to read
     */
    private static String readTemplateFile(String templateFileName) throws IOException {
        String cd2guiDir = getCd2guiProjectDirectory();

        // Construct template path directly using Path API
        Path templatePath = Paths.get(cd2guiDir, "src", "main", "resources", "tpl", "ftl", "templates", templateFileName);

        if (Files.exists(templatePath)) {
            Log.info("Found template file: " + templatePath.toString(), "SimpleFTLGenerator");
            return Files.readString(templatePath);
        }

        // If no file found, generate a simple fallback template
        Log.warn("Template file not found in cd2gui project: " + templateFileName + ", using fallback");
        return generateFallbackTemplate(templateFileName);
    }

    /**
     * Generate a simple fallback template when template file is not found.
     */
    private static String generateFallbackTemplate(String templateFileName) {
        if (templateFileName.contains("pie")) {
            return "// Get data from the current page context\n" +
                    "var ${className?lower_case}s = this.get${className}s();\n" +
                    "Map<String, Integer> distribution = new HashMap<>();\n\n" +
                    "// Process each instance to build distribution map\n" +
                    "for (${className} ${className?lower_case} : ${className?lower_case}s) {\n" +
                    "    String ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();\n" +
                    "    if (${attributeName} != null) {\n" +
                    "        distribution.put(${attributeName}, distribution.getOrDefault(${attributeName}, 0) + 1);\n" +
                    "    }\n" +
                    "}\n\n" +
                    "// Build pie chart data\n" +
                    "GemPieChartDataBuilder builder = new GemPieChartDataBuilder();\n" +
                    "for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n" +
                    "    builder.addEntry(entry.getKey(), entry.getValue());\n" +
                    "}\n\n" +
                    "return builder.build().get();";
        } else if (templateFileName.contains("bar")) {
            return "// Get data from the current page context\n" +
                    "var ${className?lower_case}s = this.get${className}s();\n" +
                    "Map<String, Integer> distribution = new HashMap<>();\n\n" +
                    "// Process each instance to build distribution map\n" +
                    "for (${className} ${className?lower_case} : ${className?lower_case}s) {\n" +
                    "    String ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();\n" +
                    "    if (${attributeName} != null) {\n" +
                    "        distribution.put(${attributeName}, distribution.getOrDefault(${attributeName}, 0) + 1);\n" +
                    "    }\n" +
                    "}\n\n" +
                    "// Build bar chart data\n" +
                    "GemBarChartDataBuilder builder = new GemBarChartDataBuilder();\n" +
                    "for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n" +
                    "    builder.addLabel(entry.getKey());\n" +
                    "    builder.addEntry(entry.getKey(), Arrays.asList(entry.getValue()));\n" +
                    "}\n\n" +
                    "return builder.build().get();";
        } else if (templateFileName.contains("line")) {
            return "// Get data from the current page context\n" +
                    "var ${className?lower_case}s = this.get${className}s();\n" +
                    "List<GemLineChartData.DataPoint> dataPoints = new ArrayList<>();\n\n" +
                    "// Process each instance to build line chart data\n" +
                    "int index = 0;\n" +
                    "for (${className} ${className?lower_case} : ${className?lower_case}s) {\n" +
                    "    String ${attributeName} = String.valueOf(${className?lower_case}.get${attributeName?cap_first}());\n" +
                    "    if (${attributeName} != null) {\n" +
                    "        dataPoints.add(new GemLineChartData.DataPoint(\"Point \" + index, Double.valueOf(index)));\n" +
                    "        index++;\n" +
                    "    }\n" +
                    "}\n\n" +
                    "// Build line chart data\n" +
                    "GemLineChartDataBuilder builder = new GemLineChartDataBuilder();\n" +
                    "for (GemLineChartData.DataPoint point : dataPoints) {\n" +
                    "    builder.addDataPoint(point.getLabel(), point.getValue());\n" +
                    "}\n\n" +
                    "return builder.build().get();";
        } else if (templateFileName.contains("scatter")) {
            return "// Get data from the current page context\n" +
                    "var ${className?lower_case}s = this.get${className}s();\n" +
                    "List<GemScatterPlotData.DataPoint> dataPoints = new ArrayList<>();\n\n" +
                    "// Process each instance to build scatter plot data\n" +
                    "int index = 0;\n" +
                    "for (${className} ${className?lower_case} : ${className?lower_case}s) {\n" +
                    "    String ${attributeName} = String.valueOf(${className?lower_case}.get${attributeName?cap_first}());\n" +
                    "    if (${attributeName} != null) {\n" +
                    "        double x = index;\n" +
                    "        double y = ${attributeName}.hashCode() % 100; // Simple numeric conversion\n" +
                    "        dataPoints.add(new GemScatterPlotData.DataPoint(x, y));\n" +
                    "        index++;\n" +
                    "    }\n" +
                    "}\n\n" +
                    "// Build scatter plot data\n" +
                    "GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();\n" +
                    "for (GemScatterPlotData.DataPoint point : dataPoints) {\n" +
                    "    builder.addDataPoint(point.getX(), point.getY());\n" +
                    "}\n\n" +
                    "return builder.build().get();";
        } else {
            // Generic fallback - return empty pie chart
            return "// Generic fallback template\n" +
                    "return new GemPieChartDataBuilder().build().get();";
        }
    }

    /**
     * Generate Java implementation files for GUI classes with real data access.
     * This generates the actual Java classes that contain chart data methods.
     */
    public static void generateGUIImplementations(Map<ASTCDClass, List<AttributeMetric<?>>> classMetricsMap) {
        if (classMetricsMap == null || classMetricsMap.isEmpty()) {
            Log.warn("No class metrics provided for GUI implementation generation");
            return;
        }

        Log.info("Generating GUI implementation files for " + classMetricsMap.size() + " classes", "FTLGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : classMetricsMap.entrySet()) {
            ASTCDClass clazz = entry.getKey();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (clazz != null && metrics != null && !metrics.isEmpty()) {
                generateGUIClassImplementation(clazz.getName(), metrics);
            }
        }
    }

    /**
     * Generate a single GUI class implementation file.
     */
    private static void generateGUIClassImplementation(String className, List<AttributeMetric<?>> metrics) {
        if (className == null || metrics == null || metrics.isEmpty()) {
            Log.warn("Invalid parameters for GUI class implementation generation");
            return;
        }

        String guiClassName = className + "Metric";
        String fileName = guiClassName + ".java";

        StringBuilder javaContent = new StringBuilder();

        // Package declaration - use gui package to match generated GUI files
        javaContent.append("package gui;\n\n");

        // Imports
        javaContent.append("import ").append(className.toLowerCase()).append(".").append(className).append(";\n");
        javaContent.append("import umlp.jsweet.extension.annotation.Component;\n");
        javaContent.append("import java.util.*;\n");
        javaContent.append("import java.util.stream.Collectors;\n\n");

        // Add chart-specific imports
        Set<String> chartImports = new HashSet<>();
        for (AttributeMetric<?> metric : metrics) {
            switch (metric.getType()) {
                case PIE_CHART:
                    chartImports.add("import mc.fenix.charts.GemPieChartData;\n");
                    chartImports.add("import mc.fenix.charts.GemPieChartDataBuilder;\n");
                    break;
                case BAR_CHART:
                    chartImports.add("import mc.fenix.charts.GemBarChartData;\n");
                    chartImports.add("import mc.fenix.charts.GemBarChartDataBuilder;\n");
                    break;
                case LINE_CHART:
                    chartImports.add("import mc.fenix.charts.GemLineChartData;\n");
                    chartImports.add("import mc.fenix.charts.GemLineChartDataBuilder;\n");
                    break;
                case SCATTER_PLOT:
                    chartImports.add("import mc.fenix.charts.GemScatterPlotData;\n");
                    chartImports.add("import mc.fenix.charts.GemScatterPlotDataBuilder;\n");
                    chartImports.add("import mc.fenix.charts.GemScatterPlotAxis;\n");
                    chartImports.add("import mc.fenix.charts.GemScatterPlotAxisBuilder;\n");
                    break;
            }
        }

        // Add all chart imports
        for (String importStatement : chartImports) {
            javaContent.append(importStatement);
        }
        javaContent.append("\n");

        // Class declaration - extends the generated TOP class
        javaContent.append("@Component\n");
        javaContent.append("public class ").append(guiClassName).append(" extends ").append(guiClassName).append("TOP {\n\n");

        // Generate chart data methods for each metric
        for (AttributeMetric<?> metric : metrics) {
            generateChartDataMethod(javaContent, className, metric);
        }

        // Close class
        javaContent.append("}\n");

        // Write to file - use calling project's build/cd2gui/guiImpl directory
        try {
            // Use current working directory (calling project) instead of cd2gui project directory
            String currentDir = System.getProperty("user.dir");
            String outputPath = currentDir + File.separator + "build" + File.separator + "cd2gui" + File.separator + "guiImpl" + File.separator + "gui";
            Files.createDirectories(Paths.get(outputPath));

            File outputFile = new File(outputPath, fileName);
            try (FileWriter writer = new FileWriter(outputFile, StandardCharsets.UTF_8)) {
                writer.write(javaContent.toString());
            }

            Log.info("Generated GUI implementation for class " + className + " with " + metrics.size() + " chart methods to " + outputPath, "GUIImplGenerator");

        } catch (IOException e) {
            Log.error("Failed to write GUI implementation file: " + fileName, e);
        }
    }

    /**
     * Generate chart data method based on metric type.
     */
    private static void generateChartDataMethod(StringBuilder javaContent, String className, AttributeMetric<?> metric) {
        String attributeName = metric.getAttributeName();
        ChartType chartType = metric.getType();

        if (attributeName == null || chartType == null || chartType == ChartType.NONE) {
            Log.warn("Invalid metric for method generation: " + attributeName + ", type: " + chartType);
            return;
        }

        switch (chartType) {
            case PIE_CHART:
                generatePieChartDataMethod(javaContent, className, attributeName);
                break;
            case SCATTER_PLOT:
                generateScatterPlotDataMethod(javaContent, className, attributeName);
                break;
            default:
                Log.warn("Unsupported chart type for method generation: " + chartType);
                break;
        }
    }

    /**
     * Generate pie chart data method using real data (following processDataForCarStatus.ftl pattern).
     */
    private static void generatePieChartDataMethod(StringBuilder javaContent, String className, String attributeName) {
        String methodName = "getPieChartData4" + className + capitalize(attributeName);

        javaContent.append("    /**\n");
        javaContent.append("     * Generates pie chart data for ").append(attributeName).append(" distribution.\n");
        javaContent.append("     * @return GemPieChartData for ").append(attributeName).append(" attribute\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemPieChartData ").append(methodName).append("() {\n");

        // Generate data retrieval logic - use page data
        javaContent.append("        // Get data from the current page context\n");
        javaContent.append("        var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        javaContent.append("        Map<String, Integer> distribution = new HashMap<>();\n\n");

        javaContent.append("        // Process each instance to build distribution map\n");
        javaContent.append("        for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        javaContent.append("            // Enum attribute - group by enum values\n");
        javaContent.append("            String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("().toString();\n");
        javaContent.append("            if (").append(attributeName).append(" != null) {\n");
        javaContent.append("                distribution.put(").append(attributeName).append(", distribution.getOrDefault(").append(attributeName).append(", 0) + 1);\n");
        javaContent.append("            }\n");
        javaContent.append("        }\n\n");

        // Generate chart data building
        javaContent.append("        // Build pie chart data\n");
        javaContent.append("        GemPieChartDataBuilder builder = new GemPieChartDataBuilder();\n");
        javaContent.append("        for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n");
        javaContent.append("            builder.addEntry(entry.getKey(), entry.getValue());\n");
        javaContent.append("        }\n\n");
        javaContent.append("        return builder.build().get();\n");
        javaContent.append("    }\n\n");
    }

    /**
     * Generate scatter plot data method using real data (following processDataForCarMileage.ftl pattern).
     */
    private static void generateScatterPlotDataMethod(StringBuilder javaContent, String className, String attributeName) {
        String methodName = "getScatterPlotData4" + className + capitalize(attributeName);
        String xAxisMethodName = "getScatterPlotXAxis4" + className + capitalize(attributeName);
        String yAxisMethodName = "getScatterPlotYAxis4" + className + capitalize(attributeName);

        // Generate scatter plot data method
        javaContent.append("    /**\n");
        javaContent.append("     * Generates scatter plot data for ").append(attributeName).append(" correlation analysis.\n");
        javaContent.append("     * @return GemScatterPlotData for ").append(attributeName).append(" attribute\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemScatterPlotData ").append(methodName).append("() {\n");

        // Generate scatter plot implementation with real data
        javaContent.append("        // Get data from the current page context\n");
        javaContent.append("        var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        javaContent.append("        List<GemScatterPlotData.DataPoint> dataPoints = new ArrayList<>();\n\n");
        javaContent.append("        // Process each instance to build scatter plot data\n");
        javaContent.append("        for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        javaContent.append("            // Numeric attribute - create scatter points\n");
        javaContent.append("            Number ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        javaContent.append("            if (").append(attributeName).append(" != null) {\n");
        javaContent.append("                // Use index as X coordinate and attribute value as Y coordinate\n");
        javaContent.append("                double x = dataPoints.size();\n");
        javaContent.append("                double y = ").append(attributeName).append(".doubleValue();\n");
        javaContent.append("                dataPoints.add(new GemScatterPlotData.DataPoint(x, y));\n");
        javaContent.append("            }\n");
        javaContent.append("        }\n\n");
        javaContent.append("        // Build scatter plot data\n");
        javaContent.append("        GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();\n");
        javaContent.append("        for (GemScatterPlotData.DataPoint point : dataPoints) {\n");
        javaContent.append("            builder.addDataPoint(point.getX(), point.getY());\n");
        javaContent.append("        }\n\n");
        javaContent.append("        return builder.build().get();\n");
        javaContent.append("    }\n\n");

        // Generate X axis method
        javaContent.append("    /**\n");
        javaContent.append("     * Generates X axis configuration for ").append(attributeName).append(" scatter plot.\n");
        javaContent.append("     * @return GemScatterPlotAxis for X axis\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemScatterPlotAxis ").append(xAxisMethodName).append("() {\n");
        javaContent.append("        return new GemScatterPlotAxisBuilder()\n");
        javaContent.append("            .label(\"Index\")\n");
        javaContent.append("            .minValue(0.0)\n");
        javaContent.append("            .maxValue(20.0)\n");
        javaContent.append("            .stepWidth(1.0)\n");
        javaContent.append("            .build().get();\n");
        javaContent.append("    }\n\n");

        // Generate Y axis method
        javaContent.append("    /**\n");
        javaContent.append("     * Generates Y axis configuration for ").append(attributeName).append(" scatter plot.\n");
        javaContent.append("     * @return GemScatterPlotAxis for Y axis\n");
        javaContent.append("     */\n");
        javaContent.append("    public GemScatterPlotAxis ").append(yAxisMethodName).append("() {\n");
        javaContent.append("        return new GemScatterPlotAxisBuilder()\n");
        javaContent.append("            .label(\"").append(capitalize(attributeName)).append("\")\n");
        javaContent.append("            .minValue(0.0)\n");
        javaContent.append("            .maxValue(200000.0)\n");
        javaContent.append("            .stepWidth(20000.0)\n");
        javaContent.append("            .build().get();\n");
        javaContent.append("    }\n\n");
    }

    /**
     * Capitalizes the first letter of a string.
     * Used for generating method names and variable names.
     *
     * @param str Input string
     * @return String with first letter capitalized
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
