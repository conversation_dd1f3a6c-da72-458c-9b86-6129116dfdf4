${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getLineChartData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemLineChart(
data = ${methodName}(),
    enableBackgroundColor = ${attributeMetric.getDetail().isBackgroundColorEnabled()?c},
    maxValue = ${attributeMetric.getDetail().getMaxValue()},
    minValue = ${attributeMetric.getDetail().getMinValue()}
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
