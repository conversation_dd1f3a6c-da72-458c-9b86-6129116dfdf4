${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getScatterPlotData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemScatterPlot(
data = ${methodName}(),
    xAxis = @GemScatterPlotAxisBuilder()
        .label("${attributeMetric.getAttributeName()} X")
        .minValue(0.0)
        .maxValue(100.0)
        .stepWidth(10.0)
        .build(),
    yAxis = @GemScatterPlotAxisBuilder()
        .label("${attributeMetric.getAttributeName()} Y")
        .minValue(0.0)
        .maxValue(100.0)
        .stepWidth(10.0)
        .build()
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
