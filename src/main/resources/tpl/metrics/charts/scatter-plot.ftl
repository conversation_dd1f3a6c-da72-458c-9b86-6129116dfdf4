${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getScatterPlotData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemScatterPlot(
data = ${methodName}(),
    xAxis = ${attributeMetric.getDetail().getXAxis()},
    yAxis = ${attributeMetric.getDetail().getYAxis()}
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
