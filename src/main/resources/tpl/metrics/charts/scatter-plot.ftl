${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getScatterPlotData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>
<#assign xAxisMethodName = "getXAxis4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>
<#assign yAxisMethodName = "getYAxis4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemScatterPlot(
data = ${methodName}(),
    xAxis = ${xAxisMethodName}(),
    yAxis = ${yAxisMethodName}()
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
