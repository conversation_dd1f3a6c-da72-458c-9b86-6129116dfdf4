${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getScatterPlotData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemScatterPlot(
data = ${methodName}(),
    xAxis = @GemScatterPlotAxis(label = "X Axis"),
    yAxis = @GemScatterPlotAxis(label = "Y Axis")
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
