${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign className = domainClass.getName()>
<#assign attributeName = attributeMetric.getAttributeName()>
<#assign capitalizedAttributeName = attributeName?cap_first>

@GemScatterPlot(
data = processDataFor${className}${capitalizedAttributeName}(),
    xAxis = getScatterPlotXAxis4${className}${capitalizedAttributeName}(),
    yAxis = getScatterPlotYAxis4${className}${capitalizedAttributeName}()
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
