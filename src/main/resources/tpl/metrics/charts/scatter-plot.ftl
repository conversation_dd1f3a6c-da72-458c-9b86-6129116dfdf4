${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getScatterPlotData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>
<#assign scatterPlotDetail = attributeMetric.getDetail()>

@GemScatterPlot(
data = ${methodName}(),
    xAxis = scatterPlotDetail.getXAxis(),
    yAxis = scatterPlotDetail.getYAxis()
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
