${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getScatterPlotData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>
<#assign xAxisMethodName = "getScatterPlotXAxis4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>
<#assign yAxisMethodName = "getScatterPlotYAxis4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemScatterPlot(
data = ${methodName}(),
    xAxis = ${xAxisMethodName}(),
    yAxis = ${yAxisMethodName}()
)

<#else>
@GemText(
    value = "No chart data available"
)
</#if>
