${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
<#assign methodName = "getBarChartData4" + domainClass.getName() + attributeMetric.getAttributeName()?cap_first>

@GemBarChart(
data = ${methodName}(),
    stacked = ${attributeMetric.getDetail().isStacked()?c},
    maxValue = ${attributeMetric.getDetail().getMaxValue()},
    minValue = ${attributeMetric.getDetail().getMinValue()}
)
<#else>
@GemText(
    value = "No chart data available"
)
</#if>

