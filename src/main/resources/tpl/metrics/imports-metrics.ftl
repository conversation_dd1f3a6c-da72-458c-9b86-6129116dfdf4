<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "classMetrics")}

import mc.fenix.arrange.GemCard;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemGrid;
import mc.fenix.basic.GemText;
import mc.fenix.basic.GemButton;
import mc.fenix.navigation.GemNavItem;
import mc.fenix.table.GemTable;
import mc.fenix.table.GemTableHeader;
import mc.fenix.table.GemTableRow;


<#-- Chart components -->
import mc.fenix.charts.GemBarChart;
import mc.fenix.charts.GemBarChartTypes.GemBarChartData;

import mc.fenix.charts.GemBulletChart;

import mc.fenix.charts.GemCandlestickChart;
import mc.fenix.charts.GemCandlestickChartTypes.GemData2CandlestickChart;

import mc.fenix.charts.GemGaugeChart;
import mc.fenix.charts.GemGaugeChartTypes.GemGaugeChartData;

import mc.fenix.charts.GemHeatmapChart;
import mc.fenix.charts.GemHeatmapChartTypes.GemHeatmapChartData;

import mc.fenix.charts.GemLineChart;
import mc.fenix.charts.LineChartTypes.GemLineChartData;
import mc.fenix.charts.LineChartTypes.GemLineChartEntry;

import mc.fenix.charts.GemPieChart;
import mc.fenix.charts.GemPieChartTypes.GemPieChartData;
import mc.fenix.charts.GemPieChartTypes.GemPieChartEntry;

import mc.fenix.charts.GemSunburstChart;
import mc.fenix.charts.GemSunburstChartTypes.GemSunburstData;
import mc.fenix.charts.GemSunburstChartTypes.GemSDNshort;

import mc.fenix.charts.GemScatterPlot;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotData;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotDataBuilder;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotSet;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotSetBuilder;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotPoint;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotShape;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotAxis;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotAxisBuilder;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotAxisBuilder;
