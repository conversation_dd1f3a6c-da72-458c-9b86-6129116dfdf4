<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric??>
  <#assign chartType = attributeMetric.getType()>
  <#assign className = domainClass.getName()>
  <#assign attributeName = attributeMetric.getAttributeName()>
  <#assign capitalizedAttributeName = attributeName?cap_first>

  <#-- Call GUIImplGenerator generated Java implementation methods -->
  <#switch chartType>
    <#case "PIE_CHART">
      @GemPieChart(data = processDataFor${className}${capitalizedAttributeName}())
      <#break>
    <#case "BAR_CHART">
      @GemBarChart(data = processDataFor${className}${capitalizedAttributeName}())
      <#break>
    <#case "LINE_CHART">
      @GemLineChart(data = processDataFor${className}${capitalizedAttributeName}())
      <#break>
    <#case "SCATTER_PLOT">
      @GemScatterPlot(
        data = processDataFor${className}${capitalizedAttributeName}(),
        xAxis = getScatterPlotXAxis4${className}${capitalizedAttributeName}(),
        yAxis = getScatterPlotYAxis4${className}${capitalizedAttributeName}()
      )
      <#break>
    <#default>
      @GemText(value = "Chart type ${chartType} not supported")
  </#switch>
<#else>
  @GemText(value = "No attribute metric available")
</#if>
</#assign>





<#if attributeMetric??>
${name?uncap_first}_${attributeMetric.getAttributeName()}ChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "${attributeMetric.getAttributeName()} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
    ]
  )
)
<#else>
${name?uncap_first}_NoDataChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "No Data Available",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      @GemText(
        value = "No metric data available for this attribute"
      )
    ]
  )
)
</#if>
