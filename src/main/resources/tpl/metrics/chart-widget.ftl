<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric??>
  <#assign chartType = attributeMetric.getType()>
  <#assign className = domainClass.getName()>
  <#assign attributeName = attributeMetric.getAttributeName()>
  <#assign capitalizedAttributeName = attributeName?cap_first>

  <#-- Call FTL template generated chart data methods with mock data -->
  <#switch chartType>
    <#case "PIE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.pie-chart", [domainClass, name, attributeMetric])}
      <#break>
    <#case "BAR_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bar-chart", [domainClass, name, attributeMetric])}
      <#break>
    <#case "LINE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.line-chart", [domainClass, name, attributeMetric])}
      <#break>
    <#case "SCATTER_PLOT">
      ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [domainClass, name, attributeMetric])}
      <#break>
    <#default>
      ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
  </#switch>
 <#else>
  @GemText(value = "No attribute metric available")
</#if>
</#assign>





<#if attributeMetric??>
${name?uncap_first}_${attributeMetric.getAttributeName()}ChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "${attributeMetric.getAttributeName()} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
    ]
  )
)
<#else>
${name?uncap_first}_NoDataChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "No Data Available",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      @GemText(
        value = "No metric data available for this attribute"
      )
    ]
  )
)
</#if>
