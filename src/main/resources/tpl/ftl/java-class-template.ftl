<#-- Java implementation class template for GUI metrics -->
<#-- This template generates complete Java classes with chart data methods -->

package gui;

import ${domainPackage}.${className};
import umlp.jsweet.extension.annotation.Component;
import java.util.*;
import java.util.stream.Collectors;

<#-- Import chart libraries based on used chart types -->
<#list chartTypes as chartType>
<#switch chartType>
<#case "PIE_CHART">
import mc.fenix.charts.GemPieChartData;
import mc.fenix.charts.GemPieChartDataBuilder;
<#break>
<#case "BAR_CHART">
import mc.fenix.charts.GemBarChartData;
import mc.fenix.charts.GemBarChartDataBuilder;
<#break>
<#case "LINE_CHART">
import mc.fenix.charts.GemLineChartData;
import mc.fenix.charts.GemLineChartDataBuilder;
import mc.fenix.charts.GemLineChartEntry;
import mc.fenix.charts.GemLineChartEntryBuilder;
<#break>
<#case "SCATTER_PLOT">
import mc.fenix.charts.GemScatterPlotData;
import mc.fenix.charts.GemScatterPlotDataBuilder;
import mc.fenix.charts.GemScatterPlotPoint;
import mc.fenix.charts.GemScatterPlotSet;
import mc.fenix.charts.GemScatterPlotSetBuilder;
<#break>
</#switch>
</#list>

/**
 * Generated GUI implementation class for ${className} metrics.
 * Contains chart data methods for visualization.
 * 
 * @generated by CD2GUI GUIImplGenerator
 */
@Component
public class ${className}Metric extends ${className}MetricTOP {

<#-- Generate chart data methods for each metric -->
<#list metrics as metric>
    /**
     * Generates ${metric.type?lower_case?replace("_", " ")} data for ${metric.attributeName} attribute.
     * @return Chart data for ${metric.attributeName} visualization
     */
    public ${getReturnType(metric.type)} get${metric.type?cap_first}Data4${className}${metric.attributeName?cap_first}() {
<#switch metric.type>
<#case "PIE_CHART">
        <#include "templates/pie-chart-template.ftl">
<#break>
<#case "BAR_CHART">
        <#include "templates/bar-chart-template.ftl">
<#break>
<#case "LINE_CHART">
        <#include "templates/line-chart-template.ftl">
<#break>
<#case "SCATTER_PLOT">
        <#include "templates/scatter-plot-template.ftl">
<#break>
<#default>
        // Default implementation for ${metric.type}
        return null;
</#switch>
    }

</#list>
}

<#-- Helper function to determine return type based on chart type -->
<#function getReturnType chartType>
<#switch chartType>
<#case "PIE_CHART">
<#return "GemPieChartData">
<#case "BAR_CHART">
<#return "GemBarChartData">
<#case "LINE_CHART">
<#return "GemLineChartData">
<#case "SCATTER_PLOT">
<#return "GemScatterPlotData">
<#default>
<#return "Object">
</#switch>
</#function>
