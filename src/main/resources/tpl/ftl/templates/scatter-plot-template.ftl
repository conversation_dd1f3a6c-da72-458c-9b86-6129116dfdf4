<#-- <PERSON>atter plot data processing method template -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();

// Build scatter plot data with sample implementation
List<GemScatterPlotPoint> points = new ArrayList<>();
GemScatterPlotPoint samplePoint = new GemScatterPlotPoint(1.0, 2.0, "Sample", Optional.empty());
points.add(samplePoint);

GemScatterPlotSet dataSet = new GemScatterPlotSetBuilder()
    .label("${attributeName?cap_first} Data")
    .points(points)
    .color(Optional.of("blue"))
    .build().get();

GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
GemScatterPlotData scatterPlotData = builder.build().get();
scatterPlotData.addSets(dataSet);

return scatterPlotData;
