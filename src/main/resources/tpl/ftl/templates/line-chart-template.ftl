<#-- Line chart data processing method template -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();

// Build line chart data with sample implementation
List<GemLineChartEntry> entries = new ArrayList<>();
List<Double> sampleData = Arrays.asList(1.0, 2.0, 3.0);

GemLineChartEntry entry = new GemLineChartEntryBuilder()
    .label("${attributeName?cap_first} Data")
    .data(sampleData)
    .build().get();
entries.add(entry);

GemLineChartDataBuilder builder = new GemLineChartDataBuilder();
builder.entries(entries);

return builder.build().get();
