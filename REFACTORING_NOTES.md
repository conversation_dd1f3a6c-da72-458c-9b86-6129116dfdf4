# FTLGenerator 重构说明

## 重构前的问题

原始的 `FTLGenerator.java` 存在以下问题：

1. **过度使用StringBuilder**: 大量使用StringBuilder拼接字符串，代码可读性差，维护困难
2. **与现有模板系统脱节**: 项目已有完整的FreeMarker模板系统，但FTLGenerator没有利用这个系统
3. **重复代码**: 各个图表类型的生成方法有大量重复代码
4. **硬编码**: 模板内容直接硬编码在Java代码中，不够灵活
5. **缺少与现有架构的集成**: 没有与GuiModelFileCreator和GeneratorEngine集成
6. **静态方法设计**: 使用静态方法，难以进行单元测试和扩展

## 重构后的改进

### 1. 架构集成
- **与GeneratorEngine集成**: 使用现有的模板生成引擎，保持一致性
- **与GuiModelFileCreator集成**: 在metrics页面生成时自动生成FTL模板
- **使用实例方法**: 改为实例方法，便于测试和扩展

### 2. 模板化设计
- **创建模板文件**: 将硬编码的内容移到FreeMarker模板文件中
- **模块化模板**: 为不同图表类型创建独立的模板文件
- **可扩展性**: 新增图表类型只需添加对应的模板文件

### 3. 代码质量提升
- **消除重复代码**: 通过模板系统统一处理不同图表类型
- **提高可读性**: 代码逻辑更清晰，职责分离
- **增强可维护性**: 模板内容与Java代码分离

### 4. 新增文件结构

```
src/main/resources/tpl/ftl/
├── data-method.ftl                 # 主模板文件
└── methods/
    ├── pie-chart-method.ftl        # 饼图数据处理方法
    ├── bar-chart-method.ftl        # 柱状图数据处理方法
    ├── line-chart-method.ftl       # 折线图数据处理方法
    └── scatter-plot-method.ftl     # 散点图数据处理方法
```

### 5. 使用方式变化

**重构前**:
```java
// 静态方法调用，需要手动管理输出目录
FTLGenerator.generateFTLTemplates(metricsMap);
```

**重构后**:
```java
// 集成到GuiModelFileCreator中，自动调用
public void createMetricsPage() {
    // 自动生成FTL模板
    ftlGenerator.generateFTLTemplates(metricsMap);
    
    // 生成GUI页面
    // ...
}
```

### 6. 测试改进
- **添加单元测试**: 创建了FTLGeneratorTest类
- **集成测试**: 验证与现有系统的兼容性
- **错误处理测试**: 测试边界情况和错误处理

## 技术细节

### 模板引擎集成
- 使用`GeneratorEngine`和`GeneratorSetup`
- 配置`GlobalExtensionManagement`以保持一致性
- 遵循CD2GUI的模板命名和组织约定

### 方法命名约定
- 饼图: `processDataFor{ClassName}{AttributeName}()`
- 柱状图: `getBarChartData4{ClassName}{AttributeName}()`
- 折线图: `getLineChartData4{ClassName}{AttributeName}()`
- 散点图: `processDataFor{ClassName}{AttributeName}()`

### 错误处理
- 添加了完善的日志记录
- 处理空值和边界情况
- 避免重复生成相同的模板

## 向后兼容性

重构保持了向后兼容性：
- 现有的GUI生成流程不受影响
- 生成的模板文件格式保持一致
- 与UMLP-Tool的集成方式不变

## 未来扩展

重构后的设计便于未来扩展：
- 添加新图表类型只需创建对应的模板文件
- 可以轻松自定义数据处理逻辑
- 支持更复杂的模板继承和包含关系
