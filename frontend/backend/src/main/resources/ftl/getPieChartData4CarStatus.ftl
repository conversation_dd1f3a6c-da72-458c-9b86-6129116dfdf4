${tc.signature("ast", "domainClass", "status")}
${cd4c.method("public GemPieChartData getPieChartData4CarStatus()")}

var cars = this.getCars();
Map<String, Integer> distribution = new HashMap<>();

for (Car car : cars) {
    String status = car.getStatus();
    if (status != null) {
        distribution.put(status, distribution.getOrDefault(status, 0) + 1);
    }
}

GemPieChartDataBuilder builder = new GemPieChartDataBuilder();
for (Map.Entry<String, Integer> entry : distribution.entrySet()) {
    builder.addEntry(entry.getKey(), entry.getValue());
}

return builder.build().get();
