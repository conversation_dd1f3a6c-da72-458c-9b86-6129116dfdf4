${tc.signature("ast", "domainClass", "mileage")}
${cd4c.method("public GemScatterPlotData getScatterPlotData4CarMileage()")}

// Scatter plot implementation for mileage
List<GemScatterPlotPoint> points = new ArrayList<>();
GemScatterPlotPoint samplePoint = new GemScatterPlotPoint(1.0, 2.0, "Sample", Optional.empty());
points.add(samplePoint);

GemScatterPlotSet dataSet = new GemScatterPlotSetBuilder()
    .label("Sample Data")
    .points(points)
    .color(Optional.of("blue"))
    .build().get();

GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
GemScatterPlotData scatterPlotData = builder.build().get();
scatterPlotData.addSets(dataSet);
return scatterPlotData;
