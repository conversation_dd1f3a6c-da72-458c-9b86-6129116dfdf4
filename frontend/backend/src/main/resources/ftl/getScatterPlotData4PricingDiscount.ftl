${tc.signature("ast", "domainClass", "discount")}
${cd4c.method("public GemScatterPlotData getScatterPlotData4PricingDiscount()")}

// Scatter plot implementation for discount
List<GemScatterPlotPoint> points = new ArrayList<>();
GemScatterPlotPoint samplePoint = new GemScatterPlotPoint(1.0, 2.0, "Sample", Optional.empty());
points.add(samplePoint);

GemScatterPlotSet dataSet = new GemScatterPlotSetBuilder()
    .label("Sample Data")
    .points(points)
    .color(Optional.of("blue"))
    .build().get();

GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
GemScatterPlotData scatterPlotData = builder.build().get();
scatterPlotData.addSets(dataSet);
return scatterPlotData;
