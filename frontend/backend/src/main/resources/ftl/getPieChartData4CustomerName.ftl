${tc.signature("ast", "domainClass", "name")}
${cd4c.method("public GemPieChartData getPieChartData4CustomerName()")}

var customers = this.getCustomers();
Map<String, Integer> distribution = new HashMap<>();

for (Customer customer : customers) {
    String name = customer.getName();
    if (name != null) {
        distribution.put(name, distribution.getOrDefault(name, 0) + 1);
    }
}

GemPieChartDataBuilder builder = new GemPieChartDataBuilder();
for (Map.Entry<String, Integer> entry : distribution.entrySet()) {
    builder.addEntry(entry.getKey(), entry.getValue());
}

return builder.build().get();
