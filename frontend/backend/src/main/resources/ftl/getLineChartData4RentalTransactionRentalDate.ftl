${tc.signature("ast", "domainClass", "rentalDate")}
${cd4c.method("public GemLineChartData getLineChartData4RentalTransactionRentalDate()")}

// Line chart implementation for rentalDate
List<GemLineChartEntry> entries = new ArrayList<>();
List<Double> sampleData = Arrays.asList(1.0, 2.0, 3.0);
GemLineChartEntry entry = new GemLineChartEntryBuilder()
    .label("Sample Data")
    .data(sampleData)
    .build().get();
entries.add(entry);

GemLineChartDataBuilder builder = new GemLineChartDataBuilder();
builder.entries(entries);
return builder.build().get();
