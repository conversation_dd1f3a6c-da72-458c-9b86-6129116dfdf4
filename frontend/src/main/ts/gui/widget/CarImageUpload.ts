/* (c) https://github.com/MontiCore/monticore */
import { Component } from "@angular/core";
import { CarImageUploadComponent, config } from "gui/widget/CarImageUploadComponent";
import { GemButton, GemText } from "@umlp/basic";
import { GemTextInput } from "@umlp/input";

@Component({
  standalone: true,
  imports: [
    ...config.imports,
    GemButton,
    GemText,
    GemTextInput
  ],
  selector: config.selector,
  templateUrl: "CarImageUpload.html",
  styles: config.styles,
  styleUrls: config.styleUrls
})
export class CarImageUpload extends CarImageUploadComponent {

  selectedFile: File;
  imageUrl: string | ArrayBuffer;
  protected _maker: string = Math.random() + '';

  override init() {
    this.imageUrl = "assets/images/car-placeholder.png";
    super.init();
  }

  setMaker(val: string) {
    localStorage.removeItem(this._maker);
    this._maker = val;
    this.upload.emit(this._maker);
    this.saveLocal();
  }

  onFileSelected(event: Event): void {
    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files[0]) {
      this.selectedFile = fileInput.files[0];

      const reader = new FileReader();
      reader.onload = $ => {
        this.imageUrl = reader.result;
        this.saveLocal();
      };
      reader.readAsDataURL(this.selectedFile);
    }
  }

  saveLocal(): void {
    if (this.imageUrl != null) {
      localStorage.setItem(this._maker, this.imageUrl.toString());
    }
  }

}
