package gui;

import carrental.Car;
import carrental.RentableStatus;
import carrental.RentalTransaction;
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import mc.fenix.charts.gembarcharttypes.GemBarChartDataBuilder;
import mc.fenix.charts.gembarcharttypes.GemBarChartEntry;
import mc.fenix.charts.gembarcharttypes.GemBarChartEntryBuilder;
import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartDataBuilder;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntry;
import mc.fenix.charts.gemlinecharttypes.GemLineChartEntryBuilder;
import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import mc.fenix.charts.gempiecharttypes.GemPieChartDataBuilder;
import mc.fenix.charts.gempiecharttypes.GemPieChartEntry;
import mc.fenix.charts.gempiecharttypes.GemPieChartEntryBuilder;
import umlp.jsweet.extension.annotation.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Component
public class Dashboard extends DashboardTOP{

  @Override
  public GemLineChartData getRentedCarCount(List<RentalTransaction> transactions){
    // if data is not present return nothing
    if (transactions == null) {
      return null;
    }

    LocalDateTime minDate = transactions.get(0).getRentalDate();
    LocalDateTime maxDate = transactions.get(0).getRentalDate();
    for (RentalTransaction t : transactions) {
      if (t.getRentalDate().isBefore(minDate)) {
        minDate = t.getRentalDate();
      }
      if (t.getReturnDate().isPresent()
          && t.getReturnDate().get().isAfter(maxDate)) {
        maxDate = t.getRentalDate();
      }
      else if (t.getRentalDate().isAfter(maxDate)) {
        maxDate = t.getRentalDate();
      }
    }

    // create an array of dates to use them as x axis points later
    List<LocalDateTime> dates = new ArrayList<>();
    int minMonth = minDate.getMonthValue();
    int minYear = minDate.getYear();
    int maxYear = maxDate.getYear();

    List<String> labels = new ArrayList<>();

    for(;minYear<=maxYear;minYear++) {
      if(minYear != minDate.getYear()) {
        minMonth = 1;
      }
      int maxMonth = 12;
      if(minYear == maxYear) {
        maxMonth = maxDate.getMonthValue();
      }
      for(;minMonth<=maxMonth;minMonth++) {
        String minMonthRepr = String.valueOf(minMonth);
        if(minMonth<=9) {
          minMonthRepr = "0"+minMonthRepr;
        }
        labels.add(LocalDateTime.parse(minYear+"-"+minMonthRepr+"-01").toString());
      }
    }

    while (
        minDate.isBefore(maxDate) || minDate==(maxDate)
    ) {
      minDate = minDate.plusMonths(1);
      dates.add(minDate);
    }

    List<Double> dataPoints = new ArrayList<>();
    for (String label: labels) {
      dataPoints.add(0d);
    }

    GemLineChartEntry entry  = new GemLineChartEntryBuilder()
        .label("")
        .data(dataPoints)
        .build().get();

    List<GemLineChartEntry> entries = Arrays.asList(entry);
    for (int i = 0; i < dates.size(); ++i) {
      LocalDateTime date = dates.get(i);
      // find what position the mileage data needs to have to represent specific date
      for (RentalTransaction t : transactions) {
        if ((t.getRentalDate().isBefore(date) || t.getRentalDate()==(date))
            && (t.getReturnDate().isEmpty()
            || (t.getReturnDate().get().isBefore(date) || t.getReturnDate().get() == (date)))
        ) {
          entry.getDataList().set(i, entry.getData(i)+1);
        }
      }
    }

    GemLineChartData data = new GemLineChartDataBuilder().entries(entries)
        .labels(labels)
        .build().get();

    return data;
  }

  @Override
  public GemBarChartData getRentCountPerManufacturer(List<Car> cars) {
    if (cars == null) {
      return null;
    }

    // labels (x axis points) are car names
    List<String> labels = new ArrayList<>();
    for (Car c : cars) {
      if (!labels.contains(c.getManufacturer())) {
        labels.add(c.getManufacturer());
      }
    }

    List<Integer> barSizes = new ArrayList<>();
    for (String label : labels) {
      barSizes.add(0);
    }

    // create an entry for each car
    GemBarChartEntry entry  = new GemBarChartEntryBuilder()
        .label(Optional.of(""))
        .data(barSizes)
        .build().get();
    List<GemBarChartEntry> entries = Arrays.asList(entry);

    for (int i = 0; i < cars.size(); ++i) {
      Car c = cars.get(i);

      // find entry representing the car and set data for the matching index
      int id = labels.indexOf(c.getManufacturer());
      if (id >= 0) {
        entry.getDataList().set(id, entry.getData(id)+1);
      }
    }

    GemBarChartData data = new GemBarChartDataBuilder()
        .entries(entries)
        .labels(labels)
        .build().get();

    return data;
  }

  @Override
  public GemPieChartData getAvailability(List<Car> cars)  {
    if (cars == null) {
      return null;
    }

    // create 2 entries: for available and unavailable cars
    List<GemPieChartEntry> entries = new ArrayList<>();
    entries.add(new GemPieChartEntryBuilder()
        .value(0)
        .label("Available cars")
        .backgroundColor(Optional.of("green"))
        .build().get());
    entries.add(new GemPieChartEntryBuilder()
        .value(0)
        .label("Unavailable cars")
        .backgroundColor(Optional.of("red"))
        .build().get());

    int available = 0;
    int unavailable = 0;
    for (Car c : cars) {
      if (c.getStatus() == RentableStatus.AVAILABLE) {
        available++;
      }
      else {
        unavailable++;
      }
    }
    entries.get(0).setValue(available);
    entries.get(1).setValue(unavailable);

    GemPieChartData data = new GemPieChartDataBuilder().entries(entries).build().get();

    return data;
  }
}
