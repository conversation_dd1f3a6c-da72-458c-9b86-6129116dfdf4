package gui;

import def.service.ImageCacheService;
import umlp.guiutil.Router;
import umlp.jsweet.extension.annotation.Component;

import java.util.function.Consumer;

@Component(
    tsImports = {"ImageCacheService, def/service/ImageCacheService"}
)
public class CarUpdate extends CarUpdateTOP {

  ImageCacheService imageCacheService;

  public CarUpdate(ImageCacheService imageCacheService) {
    this.imageCacheService = imageCacheService;
  }

  @Override
  public Consumer<Void> toOverview() {
    return $ -> {
      Router.navigate("gui", "CarOverview");
    };
  }

  @Override
  public String getImageUrl(String maker) {
    String img = this.imageCacheService.getImageFromStorage(maker);
    if(img != null) {
      return img;
    }
    return "assets/images/" + maker + ".jpg";
  }
}
